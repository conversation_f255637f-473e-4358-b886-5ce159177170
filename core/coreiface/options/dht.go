package options

// nolint deprecated
// Deprecated: use [RoutingProvideSettings] instead.
type DhtProvideSettings = RoutingProvideSettings

// nolint deprecated
// Deprecated: use [RoutingFindProvidersSettings] instead.
type DhtFindProvidersSettings = RoutingFindProvidersSettings

// nolint deprecated
// Deprecated: use [RoutingProvideOption] instead.
type DhtProvideOption = RoutingProvideOption

// nolint deprecated
// Deprecated: use [RoutingFindProvidersOption] instead.
type DhtFindProvidersOption = RoutingFindProvidersOption

// nolint deprecated
// Deprecated: use [RoutingProvideOptions] instead.
var DhtProvideOptions = RoutingProvideOptions

// nolint deprecated
// Deprecated: use [RoutingFindProvidersOptions] instead.
var DhtFindProvidersOptions = RoutingFindProvidersOptions

// nolint deprecated
// Deprecated: use [Routing] instead.
var Dht = Routing
