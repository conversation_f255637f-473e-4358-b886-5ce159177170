package cmdenv

import (
	"strconv"
	"testing"
)

func TestEscNonPrint(t *testing.T) {
	b := []byte("hello")
	b[2] = 0x7f
	s := string(b)
	if !needEscape(s) {
		t.<PERSON><PERSON>("string needs escaping")
	}
	if !hasNonPrintable(s) {
		t.<PERSON><PERSON>("expected non-printable")
	}
	if hasNonPrintable(EscNonPrint(s)) {
		t.<PERSON><PERSON>("escaped string has non-printable")
	}
	if EscNonPrint(`hel\lo`) != `hel\\lo` {
		t.<PERSON><PERSON>("backslash not escaped")
	}

	s = `hello`
	if needEscape(s) {
		t.<PERSON><PERSON>("string does not need escaping")
	}
	if EscNonPrint(s) != s {
		t.<PERSON><PERSON>("string should not have changed")
	}
	s = `"hello"`
	if EscNonPrint(s) != s {
		t.<PERSON><PERSON>("string should not have changed")
	}
	if EscNonPrint(`"hel\"lo"`) != `"hel\\"lo"` {
		t.Fatal("did not get expected escaped string")
	}
}

func hasNonPrintable(s string) bool {
	for _, r := range s {
		if !strconv.IsPrint(r) {
			return true
		}
	}
	return false
}
