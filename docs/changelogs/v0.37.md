# Kubo changelog v0.37

<a href="https://ipshipyard.com/"><img align="right" src="https://github.com/user-attachments/assets/39ed3504-bb71-47f6-9bf8-cb9a1698f272" /></a>

This release was brought to you by the [Shipyard](https://ipshipyard.com/) team.

- [v0.37.0](#v0370)

## v0.37.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [🚦 Gateway concurrent request limits and retrieval timeouts](#-gateway-concurrent-request-limits-and-retrieval-timeouts)
  - [Clear provide queue when reprovide strategy changes](#clear-provide-queue-when-reprovide-strategy-changes)
  - [🪵 Revamped `ipfs log level` command](#-revamped-ipfs-log-level-command)
  - [📌 Named pins in `ipfs add` command](#-named-pins-in-ipfs-add-command)
  - [Custom sequence numbers in `ipfs name publish`](#custom-sequence-numbers-in-ipfs-name-publish)
  - [⚙️ `Reprovider.Strategy` is now consistently respected](#-reprovider-strategy-is-now-consistently-respected)
  - [Removed unnecessary dependencies](#removed-unnecessary-dependencies)
  - [Improved `ipfs cid`](#improved-ipfs-cid)
  - [Deprecated `ipfs stats reprovide`](#deprecated-ipfs-stats-reprovide)
  - [🔄 AutoRelay now uses all connected peers for relay discovery](#-autorelay-now-uses-all-connected-peers-for-relay-discovery)
- [📦️ Important dependency updates](#-important-dependency-updates)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

### 🔦 Highlights

#### 🚦 Gateway concurrent request limits and retrieval timeouts

New configurable limits protect gateway resources during high load:

- **[`Gateway.RetrievalTimeout`](https://github.com/ipfs/kubo/blob/master/docs/config.md#gatewayretrievaltimeout)** (default: 30s): Maximum duration for content retrieval. Returns 504 Gateway Timeout when exceeded - applies to both initial retrieval (time to first byte) and between subsequent writes.
- **[`Gateway.MaxConcurrentRequests`](https://github.com/ipfs/kubo/blob/master/docs/config.md#gatewaymaxconcurrentrequests)** (default: 4096): Limits concurrent HTTP requests. Returns 429 Too Many Requests when exceeded. Protects nodes from traffic spikes and resource exhaustion, especially useful behind reverse proxies without rate-limiting.

New Prometheus metrics for monitoring:

- `ipfs_http_gw_concurrent_requests`: Current requests being processed
- `ipfs_http_gw_responses_total`: HTTP responses by status code
- `ipfs_http_gw_retrieval_timeouts_total`: Timeouts by status code and truncation status

Tuning tips:

- Monitor metrics to understand gateway behavior and adjust based on observations
- Watch `ipfs_http_gw_concurrent_requests` for saturation
- Track `ipfs_http_gw_retrieval_timeouts_total` vs success rates to identify timeout patterns indicating routing or storage provider issues

#### Clear provide queue when reprovide strategy changes

Your content sharing strategy changes now take effect cleanly, without interference from previously queued items.

When you change [`Reprovider.Strategy`](https://github.com/ipfs/kubo/blob/master/docs/config.md#reproviderstrategy) and restart Kubo, the provide queue is automatically cleared. This ensures only content matching your new strategy will be announced to the network.

A new `ipfs provide clear` command also allows manual queue clearing for debugging purposes.

> [!NOTE]
> Upgrading to Kubo 0.37 will automatically clear any preexisting provide queue. The next time `Reprovider.Interval` hits, `Reprovider.Strategy` will be executed on a clean slate, ensuring consistent behavior with your current configuration.

#### 🪵 Revamped `ipfs log level` command

The `ipfs log level` command has been completely revamped to support both getting and setting log levels with a unified interface.

**New: Getting log levels**

- `ipfs log level` - Shows default level only
- `ipfs log level all` - Shows log level for every subsystem, including default level
- `ipfs log level foo` - Shows log level for a specific subsystem only
- Kubo RPC API: `POST /api/v0/log/level?arg=<subsystem>`

**Enhanced: Setting log levels**

- `ipfs log level foo debug` - Sets "foo" subsystem to "debug" level
- `ipfs log level all info` - Sets all subsystems to "info" level (convenient, no escaping)
- `ipfs log level '*' info` - Equivalent to above but requires shell escaping
- `ipfs log level foo default` - Sets "foo" subsystem to current default level

The command now provides full visibility into your current logging configuration while maintaining full backward compatibility. Both `all` and `*` work for specifying all subsystems, with `all` being more convenient since it doesn't require shell escaping.

#### 🧷 Named pins in `ipfs add` command

Added `--pin-name` flag to `ipfs add` for assigning names to pins.

```console
$ ipfs add --pin-name=testname cat.jpg
added bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi cat.jpg

$ ipfs pin ls --names
bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi recursive testname
```

#### ⚙️ `Reprovider.Strategy` is now consistently respected

Prior to this version, files added, blocks received etc. were "provided" to the network (announced on the DHT) regardless of the ["reproviding strategy" setting](https://github.com/ipfs/kubo/blob/master/docs/config.md#reproviderstrategy). For example:

- Strategy set to "pinned" + `ipfs add --pin=false` → file was provided regardless
- Strategy set to "roots" + `ipfs pin add` → all blocks (not only the root) were provided

Only the periodic "reproviding" action (runs every 22h by default) respected the strategy.

This was inefficient as content that should not be provided was getting provided once. Now all operations respect `Reprovider.Strategy`. If set to "roots", no blocks other than pin roots will be provided regardless of what is fetched, added etc.

> [!NOTE]
> **Behavior change:** The `--offline` flag no longer affects providing behavior. Both `ipfs add` and `ipfs --offline add` now provide blocks according to the reproviding strategy when run against an online daemon (previously `--offline add` did not provide). Since `ipfs add` has been nearly as fast as offline mode [since v0.35](https://github.com/ipfs/kubo/blob/master/docs/changelogs/v0.35.md#fast-ipfs-add-in-online-mode), `--offline` is rarely needed. To run truly offline operations, use `ipfs --offline daemon`.

#### Removed unnecessary dependencies

Kubo has been cleaned up by removing unnecessary dependencies and packages:

- Removed `thirdparty/assert` (replaced by `github.com/stretchr/testify/require`)
- Removed `thirdparty/dir` (replaced by `misc/fsutil`)
- Removed `thirdparty/notifier` (unused)
- Removed `goprocess` dependency (replaced with native Go `context` patterns)

These changes reduce the dependency footprint while improving code maintainability and following Go best practices.

#### Custom sequence numbers in `ipfs name publish`

Added `--sequence` flag to `ipfs name publish` for setting custom sequence numbers in IPNS records. This enables advanced use cases like manually coordinating updates across multiple nodes. See `ipfs name publish --help` for details.

#### Improved `ipfs cid`

Certain `ipfs cid` commands can now be run without a daemon or repository, and return correct exit code 1 on error, making it easier to perform CID conversion in scripts and CI/CD pipelines.

While at it, we also fixed unicode support in `ipfs cid bases --prefix` to correctly show `base256emoji` 🚀 :-)

#### Deprecated `ipfs stats reprovide`

The `ipfs stats reprovide` command has moved to `ipfs provide stat`. This was done to organize provider commands in one location.

> [!NOTE]
> `ipfs stats reprovide` still works, but is marked as deprecated and will be removed in a future release.

#### 🔄 AutoRelay now uses all connected peers for relay discovery

AutoRelay's relay discovery now includes all connected peers as potential relay candidates, not just peers discovered through the DHT. This allows peers connected via HTTP routing and manual `ipfs swarm connect` commands to serve as relays, improving connectivity for nodes using non-DHT routing configurations.

#### 📦️ Important dependency updates

- update `go-libp2p` to [v0.43.0](https://github.com/libp2p/go-libp2p/releases/tag/v0.43.0) (incl. [v0.42.1](https://github.com/libp2p/go-libp2p/releases/tag/v0.42.1))
- update `p2p-forge/client` to [v0.6.1](https://github.com/ipshipyard/p2p-forge/releases/tag/v0.6.1)
- update `boxo` to [v0.33.1](https://github.com/ipfs/boxo/releases/tag/v0.33.1)
- update `ipfs-webui` to [v4.8.0](https://github.com/ipfs/ipfs-webui/releases/tag/v4.8.0)
- update to [Go 1.25](https://go.dev/doc/go1.25)

### 📝 Changelog

### 👨‍👩‍👧‍👦 Contributors
