# Kubo changelog v0.25

- [v0.25.0](#v0250)

## v0.25.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [WebUI: Updated Peers View](#webui-updated-peers-view)
  - [RPC `API.Authorizations`](#rpc-apiauthorizations)
  - [MPLEX Removal](#mplex-removal)
  - [Graphsync Experiment Removal](#graphsync-experiment-removal)
  - [Commands `ipfs key sign` and `ipfs key verify`](#commands-ipfs-key-sign-and-ipfs-key-verify)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

### 🔦 Highlights

#### WebUI: Updated Peers View

WebUI [v4.2.0](https://github.com/ipfs/ipfs-webui/releases/tag/v4.2.0) shipped
with updated [ipfs-geoip](https://www.npmjs.com/package/ipfs-geoip) dataset
and [ability to filter the peers table](https://github.com/ipfs/ipfs-webui/pull/2181).

#### RPC `API.Authorizations`

Kubo RPC API now supports optional HTTP Authorization.

Granular control over user access to the RPC can be defined in the
[`API.Authorizations`](https://github.com/ipfs/kubo/blob/master/docs/config.md#apiauthorizations)
map in the configuration file, allowing different users or apps to have unique
access secrets and allowed paths.

This feature is opt-in. By default, no authorization is set up.
For configuration instructions,
refer to the [documentation](https://github.com/ipfs/kubo/blob/master/docs/config.md#apiauthorizations).

#### MPLEX Removal

After deprecating and removing mplex support by default in [v0.23.0](https://github.com/ipfs/kubo/blob/master/docs/changelogs/v0.23.md#mplex-deprecation).

We now fully removed it. If you still need mplex support to talk with other pieces of software,
please try updating them, and if they don't support yamux or QUIC [talk to us about it](https://github.com/ipfs/kubo/issues/new/choose).

Mplex is unreliable by design, it will drop data and generate errors when sending data *too fast*,
yamux and QUIC support backpressure, that means if we send data faster than the remote machine can process it, we slows down to match the remote's speed.

#### Graphsync Experiment Removal

Currently the Graphsync server is to our knowledge not used
due to lack of compatible software.
And we are left to have to maintain the go-graphsync implementation when trying
to update Kubo because some dependency changed and it fails to build anymore.

For more information see https://github.com/ipfs/kubo/pull/9747.

##### Commands `ipfs key sign` and `ipfs key verify`

This allows the Kubo node to sign arbitrary bytes to prove ownership of a PeerID or an IPNS Name. To avoid signature reuse, the signed payload is always prefixed with `libp2p-key signed message:`.

These commands are also both available through the RPC client and implemented in `client/rpc`.

For more information see https://github.com/ipfs/kubo/issues/10230.

### 📝 Changelog

<details><summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - chore: update version
  - fix: allow daemon to start correctly if the API is null (#10062) ([ipfs/kubo#10062](https://github.com/ipfs/kubo/pull/10062))
  - chore: update version
  - feat: ipfs key sign|verify (#10235) ([ipfs/kubo#10235](https://github.com/ipfs/kubo/pull/10235))
  - docs(cli): fix spelling
  - feat: webui v4.2.0 (#10241) ([ipfs/kubo#10241](https://github.com/ipfs/kubo/pull/10241))
  - Migrate coreiface ([ipfs/kubo#10237](https://github.com/ipfs/kubo/pull/10237))
  - docs: clarify WebRTCDirect cannot reuse the same port as QUIC
  - libp2p: remove mplex
  - graphsync: remove support for the server
  - docs: move kubo-specific docs (#10226) ([ipfs/kubo#10226](https://github.com/ipfs/kubo/pull/10226))
  - feat(rpc): Opt-in HTTP RPC API Authorization (#10218) ([ipfs/kubo#10218](https://github.com/ipfs/kubo/pull/10218))
  - docs: clarify ipfs id agent version
  - fix: regression in 'ipfs dns'
  - docs(changelog): clarify webrtc in v0.24
  - chore: create next changelog
  - Merge Release: v0.24.0 ([ipfs/kubo#10209](https://github.com/ipfs/kubo/pull/10209))
  - fix: allow event emitting to happen in parallel with getting the query channel
  - fixes to routing put command (#10205) ([ipfs/kubo#10205](https://github.com/ipfs/kubo/pull/10205))
  - docs: fix accelerated-dht-client
  - docs/config: remove extra commas in PublicGateways example entries
  - docs: make it clear Web RTC Direct is experimental
  - feat: add WebRTC Direct support
  - docs: update EARLY_TESTERS.md (#10194) ([ipfs/kubo#10194](https://github.com/ipfs/kubo/pull/10194))
  - Update Version: v0.24 ([ipfs/kubo#10191](https://github.com/ipfs/kubo/pull/10191))
- github.com/ipfs/boxo (v0.15.0 -> v0.16.0):
  - Release 0.16.0 ([ipfs/boxo#518](https://github.com/ipfs/boxo/pull/518))
- github.com/libp2p/go-libp2p (v0.32.1 -> v0.32.2):
  - release v0.32.2

</details>

### 👨‍👩‍👧‍👦 Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Łukasz Magiera | 149 | +7833/-2505 | 375 |
| Henrique Dias | 26 | +2498/-7535 | 210 |
| Steven Allen | 48 | +497/-373 | 129 |
| Jorropo | 9 | +247/-604 | 49 |
| Michael Muré | 6 | +306/-79 | 14 |
| Adin Schmahmann | 3 | +275/-8 | 5 |
| Lucas Molas | 1 | +181/-56 | 2 |
| Laurent Senta | 1 | +109/-24 | 7 |
| Lars Gierth | 6 | +82/-18 | 8 |
| Petar Maymounkov | 1 | +66/-32 | 3 |
| web3-bot | 1 | +47/-42 | 17 |
| Marcin Rataj | 6 | +57/-23 | 8 |
| Kevin Atkinson | 5 | +31/-31 | 17 |
| Marten Seemann | 3 | +27/-28 | 16 |
| Hector Sanjuan | 3 | +28/-14 | 10 |
| Overbool | 2 | +36/-3 | 3 |
| Raúl Kripalani | 1 | +11/-12 | 4 |
| hannahhoward | 2 | +11/-7 | 6 |
| Jeromy Johnson | 5 | +9/-9 | 5 |
| ForrestWeston | 1 | +14/-1 | 1 |
| Russell Dempsey | 1 | +10/-2 | 2 |
| Will Scott | 1 | +8/-1 | 1 |
| Jeromy | 2 | +4/-4 | 2 |
| sukun | 1 | +2/-2 | 1 |
| Steve Loeppky | 1 | +2/-2 | 1 |
| Jonas Keunecke | 1 | +2/-2 | 1 |
| Edgar Lee | 1 | +3/-1 | 1 |
| Dreamacro | 1 | +2/-2 | 2 |
| godcong | 1 | +1/-1 | 1 |
| Cole Brown | 1 | +1/-1 | 1 |
