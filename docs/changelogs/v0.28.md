# Kubo changelog v0.28

- [v0.28.0](#v0280)

## v0.28.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [RPC client: removed deprecated DHT API](#rpc-client-removed-deprecated-dht-api)
  - [Gateway: `/api/v0` is removed](#gateway-apiv0-is-removed)
  - [Removed deprecated Object API commands](#removed-deprecated-object-api-commands)
  - [No longer publishes loopback and private addresses on DHT](#no-longer-publishes-loopback-and-private-addresses-on-dht)
  - [Pin roots are now prioritized when announcing](#pin-roots-are-now-prioritized-when-announcing)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

#### RPC client: removed deprecated DHT API

The deprecated DHT API commands in the RPC client have been removed. Instead, use the Routing API.

#### Gateway: `/api/v0` is removed

The legacy subset of the Kubo RPC that was available via the Gateway port and was deprecated is now completely removed. You can read more in <https://github.com/ipfs/kubo/issues/10312>.

If you have a legacy software that relies on this behavior, and want to expose parts of `/api/v0` next to `/ipfs`, use reverse-proxy in front of Kubo to mount both Gateway and RPC on the same port. NOTE: exposing RPC to the internet comes with security risk: make sure to specify access control via [API.Authorizations](https://github.com/ipfs/kubo/blob/master/docs/config.md#apiauthorizations).

#### Removed deprecated Object API commands

The Object API commands deprecated back in [2021](https://github.com/ipfs/kubo/issues/7936) have been removed, except for `object diff`, `object patch add-link` and `object patch rm-link`, whose alternatives have not yet been built (see issues [4801](https://github.com/ipfs/kubo/issues/4801) and [4782](https://github.com/ipfs/kubo/issues/4782)).

##### Kubo ignores loopback addresses on LAN DHT and private addresses on WAN DHT

Kubo no longer keeps track of loopback and private addresses on the LAN and WAN DHTs, respectively. This means that other nodes will not try to dial likely undialable addresses.

To support testing scenarios where multiple Kubo instances run on the same machine, [`Routing.LoopbackAddressesOnLanDHT`](https://github.com/ipfs/kubo/blob/master/docs/config.md#routingloopbackaddressesonlandht) is set to `true` when the `test` profile is applied.

#### Pin roots are now prioritized when announcing

The root CIDs of pinned content are now prioritized when announcing to the Amino DHT with [`Reprovider.Strategy`](https://github.com/ipfs/kubo/blob/master/docs/config.md#reproviderstrategy) set to `all` (default) or `pinned`, making the important CIDs accessible faster.

### 📝 Changelog

<details><summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - chore: update version
  - chore: update version
  - core/node: prioritize announcing pin roots, and flat strategy (#10376) ([ipfs/kubo#10376](https://github.com/ipfs/kubo/pull/10376))
  - chore: webui v4.2.1 (#10391) ([ipfs/kubo#10391](https://github.com/ipfs/kubo/pull/10391))
  - docs(config): clarify RPC vs Gateway
  - chore: upgrade go-libp2p-kad-dht (#10378) ([ipfs/kubo#10378](https://github.com/ipfs/kubo/pull/10378))
  - chore(config): make Routing.AcceleratedDHTClient a Flag (#10384) ([ipfs/kubo#10384](https://github.com/ipfs/kubo/pull/10384))
  - fix: switch lowpower profile to autoclient
  - core: fix some typos (#10382) ([ipfs/kubo#10382](https://github.com/ipfs/kubo/pull/10382))
  - docs: fix some typos (#10377) ([ipfs/kubo#10377](https://github.com/ipfs/kubo/pull/10377))
  - core/commands!: remove deprecated object APIs (#10375) ([ipfs/kubo#10375](https://github.com/ipfs/kubo/pull/10375))
  - docs: update default ipns lifetime
  - coreapi/unixfs: don't create an additional IpfsNode for --only-hash
  - chore: cleanup old workaround (#10369) ([ipfs/kubo#10369](https://github.com/ipfs/kubo/pull/10369))
  - chore: finish reframe removal
  - docs: remove repetitive words (#10370) ([ipfs/kubo#10370](https://github.com/ipfs/kubo/pull/10370))
  - docs: updated links and refs to external resources (#10368) ([ipfs/kubo#10368](https://github.com/ipfs/kubo/pull/10368))
  - core/corehttp!: remove /api/v0 from gateway port
  - client/rpc!: remove deprecated DHT commands
  - ci: upgrade to go 1.22 (#10355) ([ipfs/kubo#10355](https://github.com/ipfs/kubo/pull/10355))
  - chore: create next changelog
  - Merge Release: v0.27.0 [skip changelog] ([ipfs/kubo#10362](https://github.com/ipfs/kubo/pull/10362))
  - test: cleanup content blocking tests (#10360) ([ipfs/kubo#10360](https://github.com/ipfs/kubo/pull/10360))
  - docs: improve release issue template
  - chore: update version
- github.com/ipfs/boxo (v0.18.0 -> v0.19.0):
  - Release v0.19.0 ([ipfs/boxo#598](https://github.com/ipfs/boxo/pull/598))
- github.com/libp2p/go-libp2p (v0.33.0 -> v0.33.2):
  - chore: release v0.33.2 (#2755) ([libp2p/go-libp2p#2755](https://github.com/libp2p/go-libp2p/pull/2755))
  - Update quic-go to v0.42.0. Release v0.33.1 (#2741) ([libp2p/go-libp2p#2741](https://github.com/libp2p/go-libp2p/pull/2741))
- github.com/libp2p/go-libp2p-kad-dht (v0.24.4 -> v0.25.2):
  - chore: release v0.25.2 ([libp2p/go-libp2p-kad-dht#961](https://github.com/libp2p/go-libp2p-kad-dht/pull/961))
  - add ctx canceled err check ([libp2p/go-libp2p-kad-dht#960](https://github.com/libp2p/go-libp2p-kad-dht/pull/960))
  - chore: release v0.25.1
  - perf: don't buffer the output of FindProvidersAsync
  - chore: use go-libp2p-routing-helpers for tracing needs
  - fix: properly iterate in tracing for protocol messenger
  - fix: apply addrFilters in the dht (#872) ([libp2p/go-libp2p-kad-dht#872](https://github.com/libp2p/go-libp2p-kad-dht/pull/872))
  - Add provider record addresses to peerstore ([libp2p/go-libp2p-kad-dht#870](https://github.com/libp2p/go-libp2p-kad-dht/pull/870))
  - chore: release v0.25.0
  - tracing: add protocol messages client tracing
  - Enhance handleNewMessage Server Mode Logging: Convert Error Logs to Debug Level ([libp2p/go-libp2p-kad-dht#860](https://github.com/libp2p/go-libp2p-kad-dht/pull/860))
  - tracing: fix DHT keys as string attribute not being valid utf-8 ([libp2p/go-libp2p-kad-dht#859](https://github.com/libp2p/go-libp2p-kad-dht/pull/859))
  - merge: fix: issues discovered in kubo v0.21.0-rc2 (#853) ([libp2p/go-libp2p-kad-dht#853](https://github.com/libp2p/go-libp2p-kad-dht/pull/853))
  - merge: fix: issues discovered in kubo v0.21.0-rc1 (#851) ([libp2p/go-libp2p-kad-dht#851](https://github.com/libp2p/go-libp2p-kad-dht/pull/851))
  - Release v0.24.0 ([libp2p/go-libp2p-kad-dht#844](https://github.com/libp2p/go-libp2p-kad-dht/pull/844))
  - fix: don't add unresponsive DHT servers to the Routing Table (#820) ([libp2p/go-libp2p-kad-dht#820](https://github.com/libp2p/go-libp2p-kad-dht/pull/820))
  - filter local addresses (for WAN) and localhost addresses (for LAN) ([libp2p/go-libp2p-kad-dht#839](https://github.com/libp2p/go-libp2p-kad-dht/pull/839))
- github.com/multiformats/go-multiaddr (v0.12.2 -> v0.12.3):
  - chore: release v0.12.3 ([multiformats/go-multiaddr#240](https://github.com/multiformats/go-multiaddr/pull/240))
  - chore: Expand comment ForEach ([multiformats/go-multiaddr#238](https://github.com/multiformats/go-multiaddr/pull/238))
  - .Decapsulate by Components ([multiformats/go-multiaddr#239](https://github.com/multiformats/go-multiaddr/pull/239))
- github.com/whyrusleeping/cbor-gen (v0.0.0-**************-66e95c3e8a87 -> v0.1.0):
  - Nullable ints (#93) ([whyrusleeping/cbor-gen#93](https://github.com/whyrusleeping/cbor-gen/pull/93))
  - Introduce Gen{} struct for configurability ([whyrusleeping/cbor-gen#94](https://github.com/whyrusleeping/cbor-gen/pull/94))
  - Transparent encoding ([whyrusleeping/cbor-gen#91](https://github.com/whyrusleeping/cbor-gen/pull/91))
  - turn max length consts into global vars ([whyrusleeping/cbor-gen#92](https://github.com/whyrusleeping/cbor-gen/pull/92))

</details>

### 👨‍👩‍👧‍👦 Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Henrique Dias | 19 | +867/-2806 | 96 |
| Rod Vagg | 7 | +921/-475 | 25 |
| Marcin Rataj | 8 | +358/-344 | 18 |
| Guillaume Michel - guissou | 1 | +145/-485 | 13 |
| Jorropo | 8 | +429/-136 | 22 |
| Łukasz Magiera | 4 | +284/-48 | 11 |
| whyrusleeping | 1 | +90/-90 | 2 |
| Michael Muré | 2 | +48/-73 | 9 |
| Marco Munizaga | 6 | +86/-29 | 10 |
| guillaumemichel | 3 | +93/-1 | 3 |
| Marten Seemann | 1 | +31/-4 | 4 |
| godeamon | 3 | +11/-8 | 3 |
| shuangcui | 1 | +6/-6 | 5 |
| occupyhabit | 1 | +3/-3 | 3 |
| crazehang | 1 | +2/-2 | 1 |
| Dennis Trautwein | 1 | +1/-2 | 1 |
| “GheisMohammadi” | 1 | +1/-1 | 1 |
| web3-bot | 1 | +2/-0 | 1 |
| Daniel Norman | 1 | +1/-1 | 1 |
