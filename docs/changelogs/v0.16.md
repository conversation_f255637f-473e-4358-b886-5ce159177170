# Kubo changelog v0.16

## v0.16.0

### Overview

Below is an outline of all that is in this release, so you get a sense of all that's included.

- [Kubo changelog v0.16](#kubo-changelog-v016)
  - [v0.16.0](#v0160)
    - [Overview](#overview)
    - [🔦 Highlights](#-highlights)
      - [🛣️ More configurable delegated routing system](#️-more-configurable-delegated-routing-system)
      - [🌍 WebTransport new experimental Transport](#-webtransport-new-experimental-transport)
      - [🗃️ Hardened IPNS record verification](#-hardened-ipns-record-verification)
      - [🌉 Web Gateways now support _redirects files](#-web-gateways-now-support-_redirects-files)
      - [😻 Add files to MFS with ipfs add --to-files](#-add-files-to-mfs-with-ipfs-add---to-files)
    - [Changelog](#changelog)
    - [Contributors](#contributors)


### 🔦 Highlights

<!-- TODO -->

#### 🛣️ More configurable delegated routing system

Since Kubo v0.14.0 [Reframe protocol](https://github.com/ipfs/specs/tree/main/reframe#readme) has been supported as a new routing system.

Now, we allow to configure several routers working together, so you can have several `reframe` and `dht` routers making queries. You can use the special `parallel` and `sequential` routers to fill your needs.

Example configuration usage using the [Filecoin Network Indexer](https://docs.cid.contact/filecoin-network-indexer/overview) and the DHT, making first a query to the indexer, and timing out after 3 seconds.

```console
$ ipfs config Routing.Type --json '"custom"'

$ ipfs config Routing.Routers.CidContact --json '{
  "Type": "reframe",
  "Parameters": {
    "Endpoint": "https://cid.contact/reframe"
  }
}'

$ ipfs config Routing.Routers.WanDHT --json '{
  "Type": "dht",
  "Parameters": {
    "Mode": "auto",
    "PublicIPNetwork": true,
    "AcceleratedDHTClient": false
  }
}'

$ ipfs config Routing.Routers.ParallelHelper --json '{
  "Type": "parallel",
  "Parameters": {
    "Routers": [
        {
        "RouterName" : "CidContact",
        "IgnoreErrors" : true,
        "Timeout": "3s"
        },
        {
        "RouterName" : "WanDHT",
        "IgnoreErrors" : false,
        "Timeout": "5m",
        "ExecuteAfter": "2s"
        }
    ]
  }
}'

$ ipfs config Routing.Methods --json '{
      "find-peers": {
        "RouterName": "ParallelHelper"
      },
      "find-providers": {
        "RouterName": "ParallelHelper"
      },
      "get-ipns": {
        "RouterName": "ParallelHelper"
      },
      "provide": {
        "RouterName": "WanDHT"
      },
      "put-ipns": {
        "RouterName": "ParallelHelper"
      }
    }'

```

### 🌍 WebTransport new experimental Transport

A new feature of [`go-libp2p`](https://github.com/libp2p/go-libp2p/releases/tag/v0.23.0) is [WebTransport](https://github.com/libp2p/go-libp2p/issues/1717).

For now it is **disabled by default** and considered **experimental**.
If you find issues running it please [report them to us](https://github.com/ipfs/kubo/issues/new).

In the future Kubo will listen on WebTransport by default for anyone already listening on QUIC addresses.

WebTransport is a new transport protocol currently under development by the [IETF](https://datatracker.ietf.org/wg/webtrans/about/) and the [W3C](https://www.w3.org/TR/webtransport/), and [already implemented by Chrome](https://caniuse.com/webtransport).
Conceptually, it’s like WebSocket run over QUIC instead of TCP. Most importantly, it allows browsers to establish (secure!) connections to WebTransport servers without the need for CA-signed certificates,
thereby enabling any js-libp2p node running in a browser to connect to any kubo node, with zero manual configuration involved.

The previous alternative is websocket secure, which require installing a reverse proxy and TLS certificates manually.

#### How to enable WebTransport

Those steps are temporary and won't be needed once we make it enabled by default.

1. Enable the WebTransport transport:
   `ipfs config Swarm.Transports.Network.WebTransport --json true`
1. Add a listener address for WebTransport to your `Addresses.Swarm` key, for example:
   ```json
   [
     "/ip4/0.0.0.0/tcp/4001",
     "/ip4/0.0.0.0/udp/4001/quic",
     "/ip4/0.0.0.0/udp/4002/quic/webtransport"
   ]
   ```
1. Restart your daemon to apply the config changes.

### 🗃️ Hardened IPNS record verification

Records that do not have a valid IPNS V2 signature, or exceed the max size
limit, will no longer pass verification, and will be ignored by Kubo when
resolving `/ipns/{libp2p-key}` content paths.

Kubo continues publishing backward-compatible V1+V2 records that can be
resolved by V1-only (go-ipfs <0.9.0) clients.

More details can be found in _Backward Compatibility_, _Record Creation_, and
_Record Verification_ sections of the [updated IPNS
specification](https://github.com/ipfs/specs/pull/319/files).

### 🌉 Web Gateways now support `_redirects` files

This feature enables support for redirects, single-page applications (SPA),
custom 404 pages, and moving to IPFS-backed website hosting
[without breaking existing HTTP links](https://www.w3.org/Provider/Style/URI).

It is limited to websites hosted in web contexts with unique
[Origins](https://en.wikipedia.org/wiki/Same-origin_policy), such as
[subdomain](https://docs.ipfs.tech/how-to/address-ipfs-on-web/#subdomain-gateway) and
[DNSLink](https://docs.ipfs.tech/how-to/address-ipfs-on-web/#dnslink-gateway) gateways.
Redirect logic is evaluated only if the requested path is not in the DAG.

See more details and usage examples see
[docs.ipfs.tech: _Redirects, custom 404s, and SPA support_](https://docs.ipfs.tech/how-to/websites-on-ipfs/redirects-and-custom-404s/).

### 😻 Add files to MFS with `ipfs add --to-files`

Users no longer need to  call `ipfs files cp` after `ipfs add` to create a
reference in [MFS](https://docs.ipfs.tech/concepts/glossary/#mfs), or deal with
low level pins if they do not wish to do so. It is now possible to pass MFS
path in an optional `--to-files` to add data directly to MFS, without creating
a low level pin.

Before (Kubo <0.16.0):


```console
$ ipfs add cat.jpg
QmCID
$ ipfs files cp /ipfs/QmCID /mfs-cats/cat.jpg
$ ipfs pin rm QmCID # removing low level pin, since MFS is protecting from gc
```

Kubo 0.16.0 collapses the above steps into one:

```console
$ ipfs add --pin=false cat.jpg --to-files /mfs-cats/
```

A recursive add to MFS works too (below line will create `/lots-of-cats/` directory in MFS):

```console
$ ipfs add -r ./lots-of-cats/ --to-files /
```

For more information, see `ipfs add --help` and `ipfs files --help`.

### Changelog

<details>
<summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - fix: Set default Methods value to nil
  - docs: document remaining 0.16.0 features
  - docs: add WebTransport docs ([ipfs/kubo#9308](https://github.com/ipfs/kubo/pull/9308))
  - chore: bump version to 0.16.0-rc1
  - fix: ensure hasher is registered when using a hashing function
  - feat: add webtransport as an option transport ([ipfs/kubo#9293](https://github.com/ipfs/kubo/pull/9293))
  - feat(gateway): _redirects file support (#8890) ([ipfs/kubo#8890](https://github.com/ipfs/kubo/pull/8890))
  - docs: fix typo in changelog-v0.16.0.md
  - Readme: Rewrite introduction and featureset (#9211) ([ipfs/kubo#9211](https://github.com/ipfs/kubo/pull/9211))
  - feat: Delegated routing with custom configuration. (#9274) ([ipfs/kubo#9274](https://github.com/ipfs/kubo/pull/9274))
  - Add <protocols> to `ipfs id -h` options (#9229) ([ipfs/kubo#9229](https://github.com/ipfs/kubo/pull/9229))
  - chore: bump go-libp2p v0.23.1 ([ipfs/kubo#9285](https://github.com/ipfs/kubo/pull/9285))
  - feat(cmds/add): --to-files option automates files cp (#8927) ([ipfs/kubo#8927](https://github.com/ipfs/kubo/pull/8927))
  - docs: fix broken ENS DoH example (#9281) ([ipfs/kubo#9281](https://github.com/ipfs/kubo/pull/9281))
  -  ([ipfs/kubo#9258](https://github.com/ipfs/kubo/pull/9258))
  -  ([ipfs/kubo#9213](https://github.com/ipfs/kubo/pull/9213))
  - docs: small typo in Dockerfile
  - feat: ipfs-webui v2.18.1
  - feat: ipfs-webui v2.18.0 (#9262) ([ipfs/kubo#9262](https://github.com/ipfs/kubo/pull/9262))
  - bump go-libp2p v0.22.0 & go1.18&go1.19 ([ipfs/kubo#9244](https://github.com/ipfs/kubo/pull/9244))
  - docs: change windows choco install command to point to go-ipfs
  - fix: pass the repo directory into the ignored_commit function
  - docs(cmds): daemon: update DHTClient description
  - fix(gw): send 200 for empty files
  - docs(readme): official vs unofficial packages
  - chore: remove Gateway.PathPrefixes
  - docs(readme): update Docker section
  - docs: fix markdown syntax typo in v0.15's changelog
  - chore: Release v0.15.0 ([ipfs/kubo#9236](https://github.com/ipfs/kubo/pull/9236))
  - chore: fix undiallable api and gateway files ([ipfs/kubo#9233](https://github.com/ipfs/kubo/pull/9233))
  - chore: Bump version to 0.16.0-dev
- github.com/ipfs/go-bitswap (v0.9.0 -> v0.10.2):
  - chore: release v0.10.2
  - fix: create a copy of the protocol slice in network.processSettings
  - chore: release v0.10.1
  - fix: incorrect type in the WithTracer polyfill option
  - chore: fix incorrect log message when a bad option is passed
  - chore: release v0.10.0
  - chore: update go-libp2p v0.22.0
- github.com/ipfs/go-cid (v0.2.0 -> v0.3.2):
  - chore: release v0.3.2
  - Revert "fix: bring back, but deprecate CodecToStr and Codecs"
  - chore: release v0.2.1
  - fix: bring back, but deprecate CodecToStr and Codecs
  - run gofmt -s
  - bump go.mod to Go 1.18 and run go fix
  - chore: release v0.3.0
  - fix: return nil Bytes() if the Cid in undef
  - Add MustParse ([ipfs/go-cid#139](https://github.com/ipfs/go-cid/pull/139))
- github.com/ipfs/go-datastore (v0.5.1 -> v0.6.0):
  - Release v0.6.0 (#194) ([ipfs/go-datastore#194](https://github.com/ipfs/go-datastore/pull/194))
  - feat: add Features + datastore scoping
  - chore: Fix comment typo (#191) ([ipfs/go-datastore#191](https://github.com/ipfs/go-datastore/pull/191))
- github.com/ipfs/go-delegated-routing (v0.3.0 -> v0.6.0):
  -  ([ipfs/go-delegated-routing#53](https://github.com/ipfs/go-delegated-routing/pull/53))
  -  ([ipfs/go-delegated-routing#52](https://github.com/ipfs/go-delegated-routing/pull/52))
  - Release v0.5.2 (#50) ([ipfs/go-delegated-routing#50](https://github.com/ipfs/go-delegated-routing/pull/50))
  - Fixed serialisation issue with multiadds (#49) ([ipfs/go-delegated-routing#49](https://github.com/ipfs/go-delegated-routing/pull/49))
  - Upgrade to IPLD `0.18.0`
  - Release v0.5.0 (#47) ([ipfs/go-delegated-routing#47](https://github.com/ipfs/go-delegated-routing/pull/47))
  - feat: use GET for FindProviders (#46) ([ipfs/go-delegated-routing#46](https://github.com/ipfs/go-delegated-routing/pull/46))
  - Update provide to take an array of keys, per spec (#45) ([ipfs/go-delegated-routing#45](https://github.com/ipfs/go-delegated-routing/pull/45))
  -  ([ipfs/go-delegated-routing#44](https://github.com/ipfs/go-delegated-routing/pull/44))
  - fix: upgrade edelweiss and rerun 'go generate' (#42) ([ipfs/go-delegated-routing#42](https://github.com/ipfs/go-delegated-routing/pull/42))
  - ci: add check to ensure generated files are up-to-date (#41) ([ipfs/go-delegated-routing#41](https://github.com/ipfs/go-delegated-routing/pull/41))
  - Add Provide RPC  (#37) ([ipfs/go-delegated-routing#37](https://github.com/ipfs/go-delegated-routing/pull/37))
  - upgrade to go-log/v2 (#34) ([ipfs/go-delegated-routing#34](https://github.com/ipfs/go-delegated-routing/pull/34))
- github.com/ipfs/go-ipns (v0.1.2 -> v0.3.0):
  - fix: require V2 signatures ([ipfs/go-ipns#41](https://github.com/ipfs/go-ipns/pull/41))
  - update go-libp2p to v0.22.0, release v0.2.0 (#39) ([ipfs/go-ipns#39](https://github.com/ipfs/go-ipns/pull/39))
  - use peer.IDFromBytes instead of peer.IDFromString (#38) ([ipfs/go-ipns#38](https://github.com/ipfs/go-ipns/pull/38))
  - sync: update CI config files (#34) ([ipfs/go-ipns#34](https://github.com/ipfs/go-ipns/pull/34))
- github.com/ipfs/go-pinning-service-http-client (v0.1.1 -> v0.1.2):
  - chore: release v0.1.2
  - fix: send up to nanosecond precision
  - refactor: cleanup Sprintf for Bearer token
  - sync: update CI config files ([ipfs/go-pinning-service-http-client#21](https://github.com/ipfs/go-pinning-service-http-client/pull/21))
- github.com/ipld/edelweiss (v0.1.4 -> v0.2.0):
  - Release v0.2.0 (#60) ([ipld/edelweiss#60](https://github.com/ipld/edelweiss/pull/60))
  - feat: add cacheable modifier to methods (#48) ([ipld/edelweiss#48](https://github.com/ipld/edelweiss/pull/48))
  - adding licenses (#52) ([ipld/edelweiss#52](https://github.com/ipld/edelweiss/pull/52))
  - sync: update CI config files ([ipld/edelweiss#56](https://github.com/ipld/edelweiss/pull/56))
  - chore: replace deprecated ioutil with io/os ([ipld/edelweiss#59](https://github.com/ipld/edelweiss/pull/59))
  - Release v0.1.6
  - fix: iterate over BlueMap in deterministic order (#57) ([ipld/edelweiss#57](https://github.com/ipld/edelweiss/pull/57))
  - fix: wrap DAG-JSON serialization error (#55) ([ipld/edelweiss#55](https://github.com/ipld/edelweiss/pull/55))
  - update examples and harness
  - upgrade to go-log/v2 (#53) ([ipld/edelweiss#53](https://github.com/ipld/edelweiss/pull/53))
- github.com/ipld/go-ipld-prime (v0.17.0 -> v0.18.0):
  - Prepare v0.18.0
  - feat(bindnode): add a BindnodeRegistry utility (#437) ([ipld/go-ipld-prime#437](https://github.com/ipld/go-ipld-prime/pull/437))
  - feat(bindnode): support full uint64 range
  - chore(bindnode): remove typed functions for options
  - chore(bindnode): docs and minor tweaks
  - feat(bindnode): make Any converters work for List and Map values
  - fix(bindnode): shorten converter option names, minor perf improvements
  - fix(bindnode): only custom convert AssignNull for Any converter
  - feat(bindnode): pass Null on to nullable custom converters
  - chore(bindnode): config helper refactor w/ short-circuit
  - feat(bindnode): add AddCustomTypeAnyConverter() to handle `Any` fields
  - feat(bindnode): add AddCustomTypeXConverter() options for most scalar kinds
  - chore(bindnode): back out of reflection for converters
  - feat(bindnode): switch to converter functions instead of type
  - feat(bindnode): allow custom type conversions with options
  - feat: add release checklist (#442) ([ipld/go-ipld-prime#442](https://github.com/ipld/go-ipld-prime/pull/442))
- github.com/libp2p/go-flow-metrics (v0.0.3 -> v0.1.0):
  - introduce an API to set a mock clock (#20) ([libp2p/go-flow-metrics#20](https://github.com/libp2p/go-flow-metrics/pull/20))
  - chore: skip slow tests when short testing is specified ([libp2p/go-flow-metrics#16](https://github.com/libp2p/go-flow-metrics/pull/16))
- github.com/libp2p/go-libp2p (v0.21.0 -> v0.23.2):
  - release v0.23.2 (#1781) ([libp2p/go-libp2p#1781](https://github.com/libp2p/go-libp2p/pull/1781))
  - webtransport: return error before wrapping opened / accepted streams (#1775) ([libp2p/go-libp2p#1775](https://github.com/libp2p/go-libp2p/pull/1775))
  - release v0.23.1 (#1773) ([libp2p/go-libp2p#1773](https://github.com/libp2p/go-libp2p/pull/1773))
  - websocket: fix nil pointer in tlsClientConf (#1770) ([libp2p/go-libp2p#1770](https://github.com/libp2p/go-libp2p/pull/1770))
  - release v0.23.0 (#1764) ([libp2p/go-libp2p#1764](https://github.com/libp2p/go-libp2p/pull/1764))
  - noise: switch to proto2, use the new NoiseExtensions protobuf ([libp2p/go-libp2p#1762](https://github.com/libp2p/go-libp2p/pull/1762))
  - webtransport: add custom resolver to add SNI (#1761) ([libp2p/go-libp2p#1761](https://github.com/libp2p/go-libp2p/pull/1761))
  - swarm: skip dialing WebTransport addresses when we have QUIC addresses (#1756) ([libp2p/go-libp2p#1756](https://github.com/libp2p/go-libp2p/pull/1756))
  - webtransport: have the server send the certificates (#1757) ([libp2p/go-libp2p#1757](https://github.com/libp2p/go-libp2p/pull/1757))
  - noise: make it possible for the server to send early data (#1750) ([libp2p/go-libp2p#1750](https://github.com/libp2p/go-libp2p/pull/1750))
  - swarm: fix selection of transport for dialing (#1653) ([libp2p/go-libp2p#1653](https://github.com/libp2p/go-libp2p/pull/1653))
  - autorelay: Add a context.Context to WithPeerSource callback (#1736) ([libp2p/go-libp2p#1736](https://github.com/libp2p/go-libp2p/pull/1736))
  - webtransport: add and check the ?type=noise URL parameter (#1749) ([libp2p/go-libp2p#1749](https://github.com/libp2p/go-libp2p/pull/1749))
  - webtransport: disable HTTP origin check (#1752) ([libp2p/go-libp2p#1752](https://github.com/libp2p/go-libp2p/pull/1752))
  - noise: don't fail handshake when early data is received without handler (#1746) ([libp2p/go-libp2p#1746](https://github.com/libp2p/go-libp2p/pull/1746))
  - Add Resolver interface to transport (#1719) ([libp2p/go-libp2p#1719](https://github.com/libp2p/go-libp2p/pull/1719))
  - use new /libp2p/go-libp2p/core  pkg (#1745) ([libp2p/go-libp2p#1745](https://github.com/libp2p/go-libp2p/pull/1745))
  - yamux: pass constructors for peer resource scopes to session constructor (#1739) ([libp2p/go-libp2p#1739](https://github.com/libp2p/go-libp2p/pull/1739))
  - tcp: add an option to enable metrics (disabled by default) (#1734) ([libp2p/go-libp2p#1734](https://github.com/libp2p/go-libp2p/pull/1734))
  - move go-libp2p-webtransport to p2p/transport/webtransport ([libp2p/go-libp2p#1737](https://github.com/libp2p/go-libp2p/pull/1737))
  - autorelay: fix race condition in TestBackoff (#1731) ([libp2p/go-libp2p#1731](https://github.com/libp2p/go-libp2p/pull/1731))
  - rcmgr: increase default connection memory limit to 32 MB (#1740) ([libp2p/go-libp2p#1740](https://github.com/libp2p/go-libp2p/pull/1740))
  - quic: update quic-go to v0.29.0 (#1723) ([libp2p/go-libp2p#1723](https://github.com/libp2p/go-libp2p/pull/1723))
  - noise: implement an API to send and receive early data ([libp2p/go-libp2p#1728](https://github.com/libp2p/go-libp2p/pull/1728))
  - identify: make the protocol version configurable (#1724) ([libp2p/go-libp2p#1724](https://github.com/libp2p/go-libp2p/pull/1724))
  - Fix threshold calculation (#1722) ([libp2p/go-libp2p#1722](https://github.com/libp2p/go-libp2p/pull/1722))
  - connmgr: use clock interface (#1720) ([libp2p/go-libp2p#1720](https://github.com/libp2p/go-libp2p/pull/1720))
  - quic: increase the buffer size used for encoding qlogs (#1715) ([libp2p/go-libp2p#1715](https://github.com/libp2p/go-libp2p/pull/1715))
  - quic: add a WithMetrics option (#1716) ([libp2p/go-libp2p#1716](https://github.com/libp2p/go-libp2p/pull/1716))
  - add default listen addresses for QUIC (#1615) ([libp2p/go-libp2p#1615](https://github.com/libp2p/go-libp2p/pull/1615))
  - feat: inject DNS resolver (#1607) ([libp2p/go-libp2p#1607](https://github.com/libp2p/go-libp2p/pull/1607))
  - connmgr: prefer peers with no streams when closing connections (#1675) ([libp2p/go-libp2p#1675](https://github.com/libp2p/go-libp2p/pull/1675))
  - quic: add DisableReuseport option (#1476) ([libp2p/go-libp2p#1476](https://github.com/libp2p/go-libp2p/pull/1476))
  - release v0.22.0 ([libp2p/go-libp2p#1688](https://github.com/libp2p/go-libp2p/pull/1688))
  - fix: don't prefer local ports from other addresses when dialing (#1673) ([libp2p/go-libp2p#1673](https://github.com/libp2p/go-libp2p/pull/1673))
  - crypto: add better support for alternative backends (#1686) ([libp2p/go-libp2p#1686](https://github.com/libp2p/go-libp2p/pull/1686))
  - crypto/secp256k1: Remove btcsuite intermediary. (#1689) ([libp2p/go-libp2p#1689](https://github.com/libp2p/go-libp2p/pull/1689))
  - Update resource manager README (#1684) ([libp2p/go-libp2p#1684](https://github.com/libp2p/go-libp2p/pull/1684))
  - move go-libp2p-core here ([libp2p/go-libp2p#1683](https://github.com/libp2p/go-libp2p/pull/1683))
  - rcmgr: make scaling changes more intuitive (#1685) ([libp2p/go-libp2p#1685](https://github.com/libp2p/go-libp2p/pull/1685))
  - move go-eventbus here ([libp2p/go-libp2p#1681](https://github.com/libp2p/go-libp2p/pull/1681))
  - basichost: remove usage of MultistreamServerMatcher in test (#1680) ([libp2p/go-libp2p#1680](https://github.com/libp2p/go-libp2p/pull/1680))
  - sync: update CI config files (#1678) ([libp2p/go-libp2p#1678](https://github.com/libp2p/go-libp2p/pull/1678))
  - move go-libp2p-resource-manager to p2p/host/resource-manager ([libp2p/go-libp2p#1677](https://github.com/libp2p/go-libp2p/pull/1677))
  - chore: preallocate slices with known final size (#1679) ([libp2p/go-libp2p#1679](https://github.com/libp2p/go-libp2p/pull/1679))
  - autorelay: fix flaky TestMaxAge (#1676) ([libp2p/go-libp2p#1676](https://github.com/libp2p/go-libp2p/pull/1676))
  - move go-libp2p-peerstore to p2p/host/peerstore ([libp2p/go-libp2p#1667](https://github.com/libp2p/go-libp2p/pull/1667))
  - examples: remove ipfs components from echo (#1672) ([libp2p/go-libp2p#1672](https://github.com/libp2p/go-libp2p/pull/1672))
  - chore: update libp2p to v0.21 in examples (#1674) ([libp2p/go-libp2p#1674](https://github.com/libp2p/go-libp2p/pull/1674))
  - change the default key type to Ed25519 (#1576) ([libp2p/go-libp2p#1576](https://github.com/libp2p/go-libp2p/pull/1576))
  - autorelay: poll for new candidates when needed ([libp2p/go-libp2p#1587](https://github.com/libp2p/go-libp2p/pull/1587))
  - examples: fix unresponsive pubsub chat example (#1652) ([libp2p/go-libp2p#1652](https://github.com/libp2p/go-libp2p/pull/1652))
  - routed: respect force direct dial context (#1665) ([libp2p/go-libp2p#1665](https://github.com/libp2p/go-libp2p/pull/1665))
  - pstoremanager: fix flaky TestClose (#1649) ([libp2p/go-libp2p#1649](https://github.com/libp2p/go-libp2p/pull/1649))
  - Allow adding prologue to noise connections (#1663) ([libp2p/go-libp2p#1663](https://github.com/libp2p/go-libp2p/pull/1663))
  - connmgr: add nowatchdog go build tag (#1666) ([libp2p/go-libp2p#1666](https://github.com/libp2p/go-libp2p/pull/1666))
  - mdns: don't discover ourselves (#1661) ([libp2p/go-libp2p#1661](https://github.com/libp2p/go-libp2p/pull/1661))
  - Support generating custom x509 certificates (#1481) ([libp2p/go-libp2p#1481](https://github.com/libp2p/go-libp2p/pull/1481))
- github.com/libp2p/go-libp2p-core (v0.19.1 -> v0.20.1):
  - chore: release v0.20.1
  - feat: forward crypto/pb
  - release v0.20.0
  - deprecate this repo
  - stop using the deprecated io/ioutil package (#279) ([libp2p/go-libp2p-core#279](https://github.com/libp2p/go-libp2p-core/pull/279))
  - use a mock clock in bandwidth tests (#276) ([libp2p/go-libp2p-core#276](https://github.com/libp2p/go-libp2p-core/pull/276))
  - remove unused MultistreamSemverMatcher (#277) ([libp2p/go-libp2p-core#277](https://github.com/libp2p/go-libp2p-core/pull/277))
  - remove peer.IDFromString (#274) ([libp2p/go-libp2p-core#274](https://github.com/libp2p/go-libp2p-core/pull/274))
  - deprecate peer.Encode in favor of peer.ID.String (#275) ([libp2p/go-libp2p-core#275](https://github.com/libp2p/go-libp2p-core/pull/275))
  - deprecate peer.ID.Pretty (#273) ([libp2p/go-libp2p-core#273](https://github.com/libp2p/go-libp2p-core/pull/273))
- github.com/libp2p/go-libp2p-kad-dht (v0.17.0 -> v0.18.0):
  - update go-libp2p to v0.22.0, release v0.18.0 ([libp2p/go-libp2p-kad-dht#788](https://github.com/libp2p/go-libp2p-kad-dht/pull/788))
  - sync: update CI config files (#789) ([libp2p/go-libp2p-kad-dht#789](https://github.com/libp2p/go-libp2p-kad-dht/pull/789))
- github.com/libp2p/go-libp2p-peerstore (v0.7.1 -> v0.8.0):
  - release v0.8.0
  - deprecate this repo
  - fix flaky TestGCDelay (#206) ([libp2p/go-libp2p-peerstore#206](https://github.com/libp2p/go-libp2p-peerstore/pull/206))
  - fix flaky EWMA test (#205) ([libp2p/go-libp2p-peerstore#205](https://github.com/libp2p/go-libp2p-peerstore/pull/205))
- github.com/libp2p/go-libp2p-record (v0.1.3 -> v0.2.0):
  - update go-libp2p to v0.22.0, release v0.2.0 ([libp2p/go-libp2p-record#50](https://github.com/libp2p/go-libp2p-record/pull/50))
  - sync: update CI config files (#47) ([libp2p/go-libp2p-record#47](https://github.com/libp2p/go-libp2p-record/pull/47))
  - increase RSA key sizes in tests ([libp2p/go-libp2p-record#44](https://github.com/libp2p/go-libp2p-record/pull/44))
  - cleanup: fix staticcheck failures ([libp2p/go-libp2p-record#43](https://github.com/libp2p/go-libp2p-record/pull/43))
- github.com/libp2p/go-libp2p-routing-helpers (v0.2.3 -> v0.4.0):
  -  ([libp2p/go-libp2p-routing-helpers#62](https://github.com/libp2p/go-libp2p-routing-helpers/pull/62))
  -  ([libp2p/go-libp2p-routing-helpers#58](https://github.com/libp2p/go-libp2p-routing-helpers/pull/58))
  - Update version.json ([libp2p/go-libp2p-routing-helpers#60](https://github.com/libp2p/go-libp2p-routing-helpers/pull/60))
  - update go-libp2p to v0.22.0 ([libp2p/go-libp2p-routing-helpers#59](https://github.com/libp2p/go-libp2p-routing-helpers/pull/59))
  - sync: update CI config files (#53) ([libp2p/go-libp2p-routing-helpers#53](https://github.com/libp2p/go-libp2p-routing-helpers/pull/53))
  - fix staticcheck ([libp2p/go-libp2p-routing-helpers#49](https://github.com/libp2p/go-libp2p-routing-helpers/pull/49))
  - fix error handling in Parallel.search ([libp2p/go-libp2p-routing-helpers#48](https://github.com/libp2p/go-libp2p-routing-helpers/pull/48))
- github.com/libp2p/go-libp2p-testing (v0.11.0 -> v0.12.0):
  - release v0.12.0 (#67) ([libp2p/go-libp2p-testing#67](https://github.com/libp2p/go-libp2p-testing/pull/67))
  - chore: update to go-libp2p v0.22.0 (#66) ([libp2p/go-libp2p-testing#66](https://github.com/libp2p/go-libp2p-testing/pull/66))
  - remove the resource manager mocks (#65) ([libp2p/go-libp2p-testing#65](https://github.com/libp2p/go-libp2p-testing/pull/65))
- github.com/libp2p/go-openssl (v0.0.7 -> v0.1.0):
  - release v0.1.0 (#31) ([libp2p/go-openssl#31](https://github.com/libp2p/go-openssl/pull/31))
  - Fix build with OpenSSL 3.0 (#25) ([libp2p/go-openssl#25](https://github.com/libp2p/go-openssl/pull/25))
  - sync: update CI config files ([libp2p/go-openssl#24](https://github.com/libp2p/go-openssl/pull/24))
  - Add openssl.DialTimeout(network, addr, timeout, ctx, flags) call ([libp2p/go-openssl#26](https://github.com/libp2p/go-openssl/pull/26))
  - Add Ctx.SetMinProtoVersion and Ctx.SetMaxProtoVersion wrappers ([libp2p/go-openssl#27](https://github.com/libp2p/go-openssl/pull/27))
  - sync: update CI config files ([libp2p/go-openssl#17](https://github.com/libp2p/go-openssl/pull/17))
  - fix: unsafe pointer passing ([libp2p/go-openssl#18](https://github.com/libp2p/go-openssl/pull/18))
  - Update test RSA cert ([libp2p/go-openssl#15](https://github.com/libp2p/go-openssl/pull/15))
  - Fix tests ([libp2p/go-openssl#16](https://github.com/libp2p/go-openssl/pull/16))
  - Address `staticcheck` issues ([libp2p/go-openssl#14](https://github.com/libp2p/go-openssl/pull/14))
  - Enabled PEM files with CRLF line endings to be used (#10) ([libp2p/go-openssl#11](https://github.com/libp2p/go-openssl/pull/11))
- github.com/libp2p/zeroconf/v2 (v2.1.1 -> v2.2.0):
  - Fix windows libp2p (#29) ([libp2p/zeroconf#29](https://github.com/libp2p/zeroconf/pull/29))
  - Fix compatibility with some IoT devices using avahi 0.8-rc1 (#27) ([libp2p/zeroconf#27](https://github.com/libp2p/zeroconf/pull/27))
  - Add TTL server option (#23) ([libp2p/zeroconf#23](https://github.com/libp2p/zeroconf/pull/23))
- github.com/lucas-clemente/quic-go (v0.28.0 -> v0.29.1):
  - http3: fix double close of chan when using DontCloseRequestStream
  - add a logging.NullTracer and logging.NullConnectionTracer ([lucas-clemente/quic-go#3512](https://github.com/lucas-clemente/quic-go/pull/3512))
  - add support for providing a custom Connection ID generator via Config (#3452) ([lucas-clemente/quic-go#3452](https://github.com/lucas-clemente/quic-go/pull/3452))
  - fix typo in README
  - fix datagram support detection (#3511) ([lucas-clemente/quic-go#3511](https://github.com/lucas-clemente/quic-go/pull/3511))
  - use a single Go routine to send copies of CONNECTION_CLOSE packets ([lucas-clemente/quic-go#3514](https://github.com/lucas-clemente/quic-go/pull/3514))
  - add YoMo to list of projects in README (#3513) ([lucas-clemente/quic-go#3513](https://github.com/lucas-clemente/quic-go/pull/3513))
  - http3: fix listening on both QUIC and TCP (#3465) ([lucas-clemente/quic-go#3465](https://github.com/lucas-clemente/quic-go/pull/3465))
  - Disable anti-amplification limit by address validation token (#3326) ([lucas-clemente/quic-go#3326](https://github.com/lucas-clemente/quic-go/pull/3326))
  - fix typo in README
  - implement a new API to let servers control client address verification ([lucas-clemente/quic-go#3501](https://github.com/lucas-clemente/quic-go/pull/3501))
  - use a generic streams map for incoming streams ([lucas-clemente/quic-go#3489](https://github.com/lucas-clemente/quic-go/pull/3489))
  - fix unreachable code after log.Fatal in fuzzing corpus generator (#3496) ([lucas-clemente/quic-go#3496](https://github.com/lucas-clemente/quic-go/pull/3496))
  - use generic Min and Max functions ([lucas-clemente/quic-go#3483](https://github.com/lucas-clemente/quic-go/pull/3483))
  - add QPACK (RFC 9204) to the list of supported RFCs (#3485) ([lucas-clemente/quic-go#3485](https://github.com/lucas-clemente/quic-go/pull/3485))
  - add a function to distinguish between long and short header packets (#3498) ([lucas-clemente/quic-go#3498](https://github.com/lucas-clemente/quic-go/pull/3498))
  - use a generic streams map for outgoing streams (#3488) ([lucas-clemente/quic-go#3488](https://github.com/lucas-clemente/quic-go/pull/3488))
  - update golangci-lint action to v3, golangci-lint to v1.48.0 (#3499) ([lucas-clemente/quic-go#3499](https://github.com/lucas-clemente/quic-go/pull/3499))
  - use a generic linked list (#3487) ([lucas-clemente/quic-go#3487](https://github.com/lucas-clemente/quic-go/pull/3487))
  - drop support for Go 1.16 and 1.17 (#3482) ([lucas-clemente/quic-go#3482](https://github.com/lucas-clemente/quic-go/pull/3482))
  - optimize FirstOutstanding in the sent packet history (#3467) ([lucas-clemente/quic-go#3467](https://github.com/lucas-clemente/quic-go/pull/3467))
  - update supported RFCs in README (#3456) ([lucas-clemente/quic-go#3456](https://github.com/lucas-clemente/quic-go/pull/3456))
  - http3: ignore context after response when using DontCloseRequestStream (#3473) ([lucas-clemente/quic-go#3473](https://github.com/lucas-clemente/quic-go/pull/3473))
- github.com/marten-seemann/webtransport-go (null -> v0.1.1):
  - release v0.1.1 (#31) ([marten-seemann/webtransport-go#31](https://github.com/marten-seemann/webtransport-go/pull/31))
  - fix double close of chan when using DontCloseRequestStream
- github.com/multiformats/go-base32 (v0.0.4 -> v0.1.0):
  - chore: bump version to 0.1.0
  - fix: fix staticcheck complaints
  - run gofmt -s
  - sync: update CI config files (#5) ([multiformats/go-base32#5](https://github.com/multiformats/go-base32/pull/5))
- github.com/multiformats/go-multiaddr (v0.6.0 -> v0.7.0):
  - Release v0.7.0 ([multiformats/go-multiaddr#183](https://github.com/multiformats/go-multiaddr/pull/183))
  - use decimal numbers for multicodecs ([multiformats/go-multiaddr#184](https://github.com/multiformats/go-multiaddr/pull/184))
  - Fix comment on Decapsulate ([multiformats/go-multiaddr#181](https://github.com/multiformats/go-multiaddr/pull/181))
  -  ([multiformats/go-multiaddr#182](https://github.com/multiformats/go-multiaddr/pull/182))
  - sync: update CI config files (#180) ([multiformats/go-multiaddr#180](https://github.com/multiformats/go-multiaddr/pull/180))
  - Add webrtc (#179) ([multiformats/go-multiaddr#179](https://github.com/multiformats/go-multiaddr/pull/179))
- github.com/multiformats/go-multicodec (v0.5.0 -> v0.6.0):
  - chore: version bump 0.6.0
  - fix: replace io/ioutil with io
  - bump go.mod to Go 1.18 and run go fix

</details>

### Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Marten Seemann | 236 | +12637/-24326 | 1152 |
| Raúl Kripalani | 118 | +11626/-4136 | 422 |
| vyzo | 144 | +10129/-3665 | 230 |
| galargh | 9 | +5293/-5298 | 26 |
| Marco Munizaga | 83 | +7502/-3080 | 147 |
| Antonio Navarro Perez | 33 | +4074/-1240 | 78 |
| Steven Allen | 98 | +1974/-1693 | 202 |
| Cole Brown | 57 | +2169/-1338 | 95 |
| Rod Vagg | 21 | +2588/-768 | 56 |
| Gus Eggert | 16 | +2011/-1226 | 36 |
| Yusef Napora | 6 | +2738/-187 | 43 |
| Raúl Kripalani | 2 | +1000/-889 | 18 |
| Łukasz Magiera | 26 | +1312/-500 | 54 |
| Will | 2 | +1593/-200 | 18 |
| Jorropo | 31 | +924/-712 | 204 |
| Juan Batiz-Benet | 2 | +1531/-9 | 21 |
| Jeromy | 14 | +691/-468 | 51 |
| Petar Maymounkov | 4 | +469/-285 | 25 |
| Jeromy Johnson | 24 | +474/-204 | 116 |
| Justin Johnson | 1 | +582/-93 | 7 |
| Aarsh Shah | 24 | +377/-105 | 34 |
| web3-bot | 18 | +246/-228 | 93 |
| Masih H. Derkani | 2 | +197/-213 | 21 |
| Marcin Rataj | 9 | +211/-176 | 16 |
| adam | 4 | +235/-49 | 9 |
| Jakub Sztandera | 9 | +203/-73 | 13 |
| Guilhem Fanton | 1 | +216/-48 | 5 |
| Lucas Molas | 1 | +219/-9 | 3 |
| Peter Argue | 1 | +166/-36 | 3 |
| Vibhav Pant | 4 | +186/-12 | 7 |
| Adrian Lanzafame | 3 | +180/-16 | 5 |
| Lars Gierth | 5 | +151/-41 | 25 |
| João Oliveirinha | 1 | +124/-38 | 11 |
| dignifiedquire | 3 | +122/-33 | 6 |
| Chinmay Kousik | 2 | +128/-4 | 7 |
| Toby | 1 | +89/-36 | 4 |
| Oleg Jukovec | 3 | +111/-14 | 8 |
| Whyrusleeping | 2 | +120/-0 | 6 |
| KevinZønda | 1 | +81/-20 | 2 |
| wzp | 2 | +86/-3 | 2 |
| Benedikt Spies | 1 | +75/-12 | 8 |
| nisainan | 1 | +33/-43 | 12 |
| Tshaka Eric Lekholoane | 1 | +57/-19 | 6 |
| cpuchip | 1 | +65/-6 | 2 |
| Roman Proskuryakov | 2 | +69/-0 | 2 |
| Arceliar | 2 | +36/-28 | 2 |
| Maxim Merzhanov | 1 | +29/-24 | 1 |
| Richard Ramos | 1 | +51/-0 | 2 |
| Dave Collins | 1 | +25/-25 | 4 |
| Leo Balduf | 2 | +37/-10 | 3 |
| David Aronchick | 1 | +42/-0 | 3 |
| Didrik Nordström | 1 | +35/-6 | 1 |
| Vasco Santos | 1 | +20/-20 | 7 |
| Jesse Bouwman | 1 | +19/-21 | 1 |
| Ivan Schasny | 2 | +22/-14 | 4 |
| MGMCN | 1 | +9/-24 | 2 |
| Brian Meek | 1 | +14/-17 | 4 |
| Ian Davis | 3 | +21/-9 | 5 |
| Mars Zuo | 1 | +7/-18 | 1 |
| RubenKelevra | 1 | +10/-10 | 1 |
| mojatter | 1 | +9/-8 | 1 |
| Cory Schwartz | 1 | +0/-17 | 1 |
| Steve Loeppky | 6 | +7/-6 | 6 |
| Matt Joiner | 2 | +10/-3 | 2 |
| Winterhuman | 2 | +7/-5 | 2 |
| Dmitry Yu Okunev | 1 | +5/-7 | 5 |
| corverroos | 1 | +7/-4 | 2 |
| Marcel Gregoriadis | 1 | +9/-0 | 1 |
| Ignacio Hagopian | 2 | +7/-2 | 2 |
| Julien Muret | 1 | +4/-4 | 2 |
| Eclésio Junior | 1 | +8/-0 | 1 |
| Stephan Eberle | 1 | +4/-3 | 1 |
| muXxer | 1 | +3/-3 | 1 |
| eth-limo | 1 | +3/-3 | 2 |
| Russell Dempsey | 2 | +4/-2 | 2 |
| Sergey | 1 | +1/-3 | 1 |
| Jun10ng | 2 | +2/-2 | 2 |
| Jorik Schellekens | 1 | +2/-2 | 1 |
| Eli Wang | 1 | +2/-2 | 1 |
| Andreas Linde | 1 | +4/-0 | 1 |
| whyrusleeping | 1 | +2/-1 | 1 |
| xiabin | 1 | +1/-1 | 1 |
| star | 1 | +0/-2 | 1 |
| fanweixiao | 1 | +1/-1 | 1 |
| dbadoy4874 | 1 | +1/-1 | 1 |
| bigs | 1 | +1/-1 | 1 |
| Tarun Bansal | 1 | +1/-1 | 1 |
| Mikerah | 1 | +1/-1 | 1 |
| Mike Goelzer | 1 | +2/-0 | 1 |
| Max Inden | 1 | +1/-1 | 1 |
| Kevin Mai-Husan Chia | 1 | +1/-1 | 1 |
| John B Nelson | 1 | +1/-1 | 1 |
| Eli Bailey | 1 | +1/-1 | 1 |
| Bryan Stenson | 1 | +1/-1 | 1 |
| Alex Stokes | 1 | +1/-1 | 1 |
| Abirdcfly | 1 | +1/-1 | 1 |
