# Kubo changelog v0.26

- [v0.26.0](#v0260)

## v0.26.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [Several deprecated commands have been removed](#several-deprecated-commands-have-been-removed)
  - [Support optional pin names](#support-optional-pin-names)
  - [`jaeger` trace exporter has been removed](#jaeger-trace-exporter-has-been-removed)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

### 🔦 Highlights

#### Kubo binary imports

For users of [Kubo preloaded plugins](https://github.com/ipfs/kubo/blob/master/docs/plugins.md#preloaded-plugins) there is now a way to create a kubo instance with your plugins by depending on the `cmd/ipfs/kubo` package rather than rebuilding kubo with the included plugins.

See the [customization docs](https://github.com/ipfs/kubo/blob/master/docs/customizing.md) for more information.

#### Several deprecated commands have been removed

Several deprecated commands have been removed:

- `ipfs urlstore` deprecated in [April 2019, Kubo 0.4.21](https://github.com/ipfs/kubo/commit/8beaee63b3fa634c59b85179286ad3873921a535), use `ipfs add -q --nocopy --cid-version=1 {url}` instead.
- `ipfs repo fsck` deprecated in [July 2019, Kubo 0.5.0](https://github.com/ipfs/kubo/commit/288a83ce7dcbf4a2498e06e4a95245bbb5e30f45)
- `ipfs file` (and `ipfs file ls`) deprecated in [November 2020, Kubo  0.8.0](https://github.com/ipfs/kubo/commit/ec64dc5c396e7114590e15909384fabce0035482), use `ipfs ls` and `ipfs files ls` instead.
- `ipfs dns` deprecated in [April 2022, Kubo 0.13](https://github.com/ipfs/kubo/commit/76ae33a9f3f9abd166d1f6f23d6a8a0511510e3c), use `ipfs resolve /ipns/{name}` instead.
- `ipfs tar` deprecated [April 2022, Kubo 0.13](https://github.com/ipfs/kubo/pull/8849)

#### Support optional pin names

You can now add a name to a pin when pinning a CID. To do so, use `ipfs pin add --name "Some Name" bafy...`. You can list your pins, including their names, with `ipfs pin ls --names`.

#### `jaeger` trace exporter has been removed

`jaeger` exporter has been removed from upstream, you should use `otlp` exporter instead.
See the [boxo tracing docs](https://github.com/ipfs/boxo/blob/a391d02102875ee7075a692076154bec1fa871f3/docs/tracing.md) for an example.

### 📝 Changelog

<details><summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - chore: update version
  - chore: update version
  - feat(pinning): allow for overwriting pin name
  - chore: update otlp
  - Revert "build,docker: add support for riscv64"
  - feat: support optional pin names (#10261) ([ipfs/kubo#10261](https://github.com/ipfs/kubo/pull/10261))
  - build,docker: add support for riscv64
  - feat(cmd/ipfs): Make it possible to depend on cmd/ipfs/kubo for easier preloaded plugin management ([ipfs/kubo#10219](https://github.com/ipfs/kubo/pull/10219))
  - docs: fix broken link in HTTP RPC client doc (#10267) ([ipfs/kubo#10267](https://github.com/ipfs/kubo/pull/10267))
  - Merge Release: v0.25.0 [skip changelog] ([ipfs/kubo#10260](https://github.com/ipfs/kubo/pull/10260))
  - docs: add detail to NOpfs instructions in content-blocking.md
  - commands: remove several deprecated commands
  - fix: allow daemon to start correctly if the API is null (#10062) ([ipfs/kubo#10062](https://github.com/ipfs/kubo/pull/10062))
  - chore: update version
- github.com/ipfs/boxo (v0.16.0 -> v0.17.0):
  - Release v0.17.0 ([ipfs/boxo#542](https://github.com/ipfs/boxo/pull/542))
- github.com/ipfs/go-ipld-cbor (v0.0.6 -> v0.1.0):
  - v0.1.0 bump
  - chore: add or force update version.json
  - allow configuration of ipldStores default hash function ([ipfs/go-ipld-cbor#86](https://github.com/ipfs/go-ipld-cbor/pull/86))
  - sync: update CI config files (#85) ([ipfs/go-ipld-cbor#85](https://github.com/ipfs/go-ipld-cbor/pull/85))
- github.com/ipfs/go-unixfsnode (v1.8.1 -> v1.9.0):
  - v1.9.0 bump
  - feat: expose ToDirEntryFrom to allow sub-dag representation
  - feat: new UnixFS{File,Directory} with options pattern
  - feat: testutil generator enhancements
- github.com/ipld/go-car/v2 (v2.10.2-0.20230622090957-499d0c909d33 -> v2.13.1):
  - fix: BlockMetadata#Offset should be for section, not block data
  - fix: add closed check, expose storage.ErrClosed
  - fix: switch constructor args to match storage.New*, make roots plural
  - feat: add DeferredCarWriter
  - feat: fix BlockReader#SkipNext & add SourceOffset property
  - v0.6.2 ([ipld/go-car#464](https://github.com/ipld/go-car/pull/464))
  - fix: opt-in way to allow empty list of roots in CAR headers ([ipld/go-car#461](https://github.com/ipld/go-car/pull/461))
- github.com/libp2p/go-libp2p-asn-util (v0.3.0 -> v0.4.1):
  - chore: release v0.4.1
  - fix: add Init method on backward compat
  - chore: release v0.4.0
  - rewrite representation to a sorted binary list and embed it
  - docs: fix incorrect markdown === in README
  - ci: run go generate on CI (#27) ([libp2p/go-libp2p-asn-util#27](https://github.com/libp2p/go-libp2p-asn-util/pull/27))
- github.com/multiformats/go-multiaddr (v0.12.0 -> v0.12.1):
  - v0.12.1 bump
  - manet: reduce allocations in resolve unspecified address
- github.com/whyrusleeping/cbor-gen (v0.0.0-20230126041949-52956bd4c9aa -> v0.0.0-20240109153615-66e95c3e8a87):
  - Add a feature to preserve nil slices (#88) ([whyrusleeping/cbor-gen#88](https://github.com/whyrusleeping/cbor-gen/pull/88))
  - some cleanup for easier reading ([whyrusleeping/cbor-gen#89](https://github.com/whyrusleeping/cbor-gen/pull/89))
  - Support gen for map with value type `string` (#83) ([whyrusleeping/cbor-gen#83](https://github.com/whyrusleeping/cbor-gen/pull/83))
  - feat: add support for pointers to CIDs in slices (#86) ([whyrusleeping/cbor-gen#86](https://github.com/whyrusleeping/cbor-gen/pull/86))
  - optimize anything using WriteString ([whyrusleeping/cbor-gen#85](https://github.com/whyrusleeping/cbor-gen/pull/85))
  - Implement *bool support and support omitempty for slices ([whyrusleeping/cbor-gen#81](https://github.com/whyrusleeping/cbor-gen/pull/81))

</details>

### 👨‍👩‍👧‍👦 Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Henrique Dias | 11 | +493/-1184 | 48 |
| Łukasz Magiera | 3 | +610/-582 | 16 |
| Rod Vagg | 11 | +1030/-151 | 18 |
| whyrusleeping | 6 | +553/-388 | 14 |
| Jorropo | 13 | +561/-348 | 84 |
| Jeromy Johnson | 1 | +771/-48 | 6 |
| Steven Allen | 2 | +264/-135 | 4 |
| Forrest | 1 | +214/-0 | 5 |
| Marcin Rataj | 1 | +89/-24 | 2 |
| sukun | 1 | +31/-11 | 5 |
| Will Scott | 3 | +25/-10 | 3 |
| Adin Schmahmann | 3 | +21/-5 | 3 |
| web3-bot | 2 | +8/-8 | 3 |
| Marten Seemann | 1 | +13/-1 | 1 |
| Bumblefudge | 1 | +5/-2 | 1 |
| Will | 1 | +1/-1 | 1 |
| Nicholas Ericksen | 1 | +1/-1 | 1 |
| 0xbasar | 1 | +1/-1 | 1 |
