# Kubo changelog v0.14

## v0.14.0

### Overview

Below is an outline of all that is in this release, so you get a sense of all that's included.

- [🛠 BREAKING CHANGES](#-breaking-changes)
  - [Removed `mdns_legacy` implementation](#removed-mdns_legacy-implementation)
- [🔦 Highlights](#-highlights)
  - [🛣️ Delegated Routing](#-delegated-routing)
  - [👥 Rename to Kubo](#-rename-to-kubo)
  - [🎒 `ipfs repo migrate`](#-ipfs-repo-migrate)
  - [🚀 Emoji support in Multibase](#-emoji-support-in-multibase)

### 🛠 BREAKING CHANGES

#### Removed `mdns_legacy` implementation

The modern DNS-SD compatible [zeroconf implementation](https://github.com/libp2p/zeroconf#readme)
(based on [this specification](https://github.com/libp2p/specs/blob/master/discovery/mdns.md))
has been running next to the `mdns_legacy` for a while (since v0.11). During
this transitional period Kubo nodes were sending twice as many LAN packets,
which ends with this release: we've [removed](https://github.com/ipfs/kubo/pull/9048) the legacy implementation.

### 🔦 Highlights

#### 🛣️ Delegated Routing

Content routing is the a term used to describe the problem of finding providers for a given piece of content.
If you have a hash, or CID of some data, how do you find who has it?
In IPFS, until now, only a DHT was used as a decentralized answer to content routing.
Now, content routing can be handled by clients implementing the [Reframe protocol](https://github.com/ipfs/specs/tree/main/reframe#readme).

Example configuration usage using the [Filecoin Network Indexer](https://docs.cid.contact/filecoin-network-indexer/overview):

```
ipfs config Routing.Routers.CidContact --json '{
  "Type": "reframe",
  "Parameters": {
    "Endpoint": "https://cid.contact/reframe"
  }
}'

```

#### 👥 Rename to Kubo

We've renamed Go-IPFS to Kubo ([details](https://github.com/ipfs/go-ipfs/issues/8959)).

Published artifacts use `kubo` now, and are available at:

- https://dist.ipfs.tech/kubo/
- https://hub.docker.com/r/ipfs/kubo/

To minimize the impact on infrastructure that autoupdates on a new release,
the same binaries are still published under the old name at:

- https://dist.ipfs.tech/go-ipfs/
- https://hub.docker.com/r/ipfs/go-ipfs/

The libp2p identify useragent of Kubo has also been changed from `go-ipfs` to `kubo`.

#### 🎒 `ipfs repo migrate`

This new command allows the you to run the repo migration without starting the daemon.

See `ipfs repo migrate --help` for more info.

#### 🚀 Emoji support in Multibase

Kubo now supports [`base256emoji`](https://github.com/multiformats/multibase/blob/master/rfcs/Base256Emoji.md) encoding in all [Multibase](https://docs.ipfs.tech/concepts/glossary/#multibase) contexts. Use it for testing Unicode support, as visual aid while explaining Multiformats, or just for fun:

```console
$ echo -n "test" | ipfs multibase encode -b base256emoji -
🚀😈✋🌈😈

$ echo -n "🚀😈✋🌈😈" | ipfs multibase decode -
test

$ ipfs cid format -v 1 -b base256emoji bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi
🚀🪐⭐💻😅❓💎🌈🌸🌚💰💍🌒😵🐶💁🤐🌎👼🙃🙅☺🌚😞🤤⭐🚀😃✈🌕😚🍻💜🐷⚽✌😊
```

[`/ipfs/🚀🪐⭐💻😅❓💎🌈🌸🌚💰💍🌒😵🐶💁🤐🌎👼🙃🙅☺🌚😞🤤⭐🚀😃✈🌕😚🍻💜🐷⚽✌😊`](https://ipfs.io/ipfs/🚀🪐⭐💻😅❓💎🌈🌸🌚💰💍🌒😵🐶💁🤐🌎👼🙃🙅☺🌚😞🤤⭐🚀😃✈🌕😚🍻💜🐷⚽✌😊)

### Changelog

<details>
<summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - chore: bump to v0.14.0
  - docs(changelog): finish v0.14.0 changelog
  - fix(gw): cache-control of index.html websites
  - chore(license): fix broken link to apache-2.0
  - fix: kubo in daemon and cli stdout
  - backport: merge commit '839b0848a' into release-v0.14.0
  - chore: Release v0.14-rc1
  - docs: fix v0.14's changelog format
  - chore: update go-multibase 🚀
  - feat(routing): Delegated Routing (#8997) ([ipfs/kubo#8997](https://github.com/ipfs/kubo/pull/8997))
  - chore: changelogs split
  - feat(gw): Cache-Control: only-if-cached
  - chore(deps): webui v2.15.1
  - Follow-ups after repository rename
 ([ipfs/kubo#9098](https://github.com/ipfs/kubo/pull/9098))
  - docs: refine wording
  - docs: refine the wording of provider strategies
  - refactor: rename to kubo
 ([ipfs/kubo#8958](https://github.com/ipfs/kubo/pull/8958))
  - fix: correct cache-control in car responses
  - docs: v0.13.1 (#9093) ([ipfs/kubo#9093](https://github.com/ipfs/kubo/pull/9093))
  - chore: update go-car ([ipfs/kubo#9089](https://github.com/ipfs/kubo/pull/9089))
  - update go-libp2p to v0.20.3 ([ipfs/kubo#9038](https://github.com/ipfs/kubo/pull/9038))
  - docs: add SECURITY.md (#9062) ([ipfs/kubo#9062](https://github.com/ipfs/kubo/pull/9062))
  - fix: remove mdns_legacy & Discovery.MDNS.Interval
  - refactor: prealloc slices with known sizes (#8892) ([ipfs/kubo#8892](https://github.com/ipfs/kubo/pull/8892))
  - docs: fix typo in `cid/base32`
  - docs: mark Swarm.ResourceMgr as experimental
  - chore: replace ioutil with io and os (#8969) ([ipfs/kubo#8969](https://github.com/ipfs/kubo/pull/8969))
  - feat: add a public function on peering to get the state
  - fix: honor url filename when downloading as CAR/BLOCK
  - Merge branch 'release'
  - chore: GitHub format
  - fix(cmd/config): make config edit subcommand work on windows
  - chore: bump Go to 1.18.3 (#9021) ([ipfs/kubo#9021](https://github.com/ipfs/kubo/pull/9021))
  - feat: upgrade to go-libp2p-kad-dht@v0.16.0 (#9005) ([ipfs/kubo#9005](https://github.com/ipfs/kubo/pull/9005))
  - docs: fix typo in the `swarm/peering` help text
  - feat: disable resource manager by default (#9003) ([ipfs/kubo#9003](https://github.com/ipfs/kubo/pull/9003))
  - fix: adjust rcmgr limits for accelerated DHT client rt refresh (#8982) ([ipfs/kubo#8982](https://github.com/ipfs/kubo/pull/8982))
  - fix(ci): make go-ipfs-as-a-library work without external peers (#8978) ([ipfs/kubo#8978](https://github.com/ipfs/kubo/pull/8978))
  - feat: log when resource manager limits are exceeded (#8980) ([ipfs/kubo#8980](https://github.com/ipfs/kubo/pull/8980))
  - fix: JS caching via Access-Control-Expose-Headers (#8984) ([ipfs/kubo#8984](https://github.com/ipfs/kubo/pull/8984))
  - docs: fix abstractions typo
  - fix: hanging goroutine in get fileArchive handler
  - chore: mark fuse experimental (#8962) ([ipfs/kubo#8962](https://github.com/ipfs/kubo/pull/8962))
  - fix(node/libp2p): disable rcmgr checkImplicitDefaults ([ipfs/kubo#8965](https://github.com/ipfs/kubo/pull/8965))
  - Add 'ipfs repo migrate' command (#8428) ([ipfs/kubo#8428](https://github.com/ipfs/kubo/pull/8428))
  - pubsub multibase encoding (#8933) ([ipfs/kubo#8933](https://github.com/ipfs/kubo/pull/8933))
  - 'pin rm' helptext: rewrite description as object is not removed from local storage (immediately) ([ipfs/kubo#8947](https://github.com/ipfs/kubo/pull/8947))
  -  ([ipfs/kubo#8934](https://github.com/ipfs/kubo/pull/8934))
  - Add instructions to resolve repo migration error (#8946) ([ipfs/kubo#8946](https://github.com/ipfs/kubo/pull/8946))
  - fix: use path instead of filepath for asset embeds to support Windows
  - chore: update version to v0.14.0-dev
- github.com/ipfs/go-bitswap (v0.6.0 -> v0.7.0):
  - chore: release v0.7.0 (#566) ([ipfs/go-bitswap#566](https://github.com/ipfs/go-bitswap/pull/566))
  - feat: coalesce and queue connection event handling (#565) ([ipfs/go-bitswap#565](https://github.com/ipfs/go-bitswap/pull/565))
  - fix initialisation example in README (#552) ([ipfs/go-bitswap#552](https://github.com/ipfs/go-bitswap/pull/552))
- github.com/ipfs/go-unixfs (v0.3.1 -> v0.4.0):
  - Set version to v0.3.2 ([ipfs/go-unixfs#122](https://github.com/ipfs/go-unixfs/pull/122))
  - Make switchToSharding more efficient
- github.com/ipld/go-ipld-prime (v0.16.0 -> v0.17.0):
  failed to fetch repo
- github.com/libp2p/go-libp2p (v0.19.4 -> v0.20.3):
  - Release 0.20.3 (#1623) ([libp2p/go-libp2p#1623](https://github.com/libp2p/go-libp2p/pull/1623))
  - release v0.20.2
  - feat: allow dialing wss peers using DNS multiaddrs
  - update go-yamux to v3.1.2, release v0.20.1 (#1591) ([libp2p/go-libp2p#1591](https://github.com/libp2p/go-libp2p/pull/1591))
  - release v0.20.0 (#1530) ([libp2p/go-libp2p#1530](https://github.com/libp2p/go-libp2p/pull/1530))
  - update go-libp2p-core, remove stream methods from network.Notifiee (#1521) ([libp2p/go-libp2p#1521](https://github.com/libp2p/go-libp2p/pull/1521))
  - autonat: return E_DIAL_REFUSED when skipping dial (#1527) ([libp2p/go-libp2p#1527](https://github.com/libp2p/go-libp2p/pull/1527))
  - move go-stream-muxer-multistream here ([libp2p/go-libp2p#1511](https://github.com/libp2p/go-libp2p/pull/1511))
  - remove dependency on go-libp2p-testing/suites/sec (#1510) ([libp2p/go-libp2p#1510](https://github.com/libp2p/go-libp2p/pull/1510))
  - backoff: fix flaky tests in backoff cache (#1516) ([libp2p/go-libp2p#1516](https://github.com/libp2p/go-libp2p/pull/1516))
  - identify: fix flaky tests (#1515) ([libp2p/go-libp2p#1515](https://github.com/libp2p/go-libp2p/pull/1515))
  - quic: increase timeout in hole punching test (#1495) ([libp2p/go-libp2p#1495](https://github.com/libp2p/go-libp2p/pull/1495))
  - Fix badge image in README (#1517) ([libp2p/go-libp2p#1517](https://github.com/libp2p/go-libp2p/pull/1517))
  - move go-libp2p-nat here ([libp2p/go-libp2p#1513](https://github.com/libp2p/go-libp2p/pull/1513))
  - move go-reuseport-transport here ([libp2p/go-libp2p#1459](https://github.com/libp2p/go-libp2p/pull/1459))
  - holepunch: fix flaky TestEndToEndSimConnect test (#1508) ([libp2p/go-libp2p#1508](https://github.com/libp2p/go-libp2p/pull/1508))
  - swarm: fix flaky TestDialExistingConnection test (#1509) ([libp2p/go-libp2p#1509](https://github.com/libp2p/go-libp2p/pull/1509))
  - tcp: limit the number of connections in tcp suite test on non-linux hosts (#1507) ([libp2p/go-libp2p#1507](https://github.com/libp2p/go-libp2p/pull/1507))
  - increase overly short require.Eventually intervals (#1501) ([libp2p/go-libp2p#1501](https://github.com/libp2p/go-libp2p/pull/1501))
  - tls: fix flaky handshake cancellation test (#1503) ([libp2p/go-libp2p#1503](https://github.com/libp2p/go-libp2p/pull/1503))
  - merge the transport test suite from go-libp2p-testing here ([libp2p/go-libp2p#1496](https://github.com/libp2p/go-libp2p/pull/1496))
  - fix racy connection comparison in TestDialWorkerLoopBasic (#1499) ([libp2p/go-libp2p#1499](https://github.com/libp2p/go-libp2p/pull/1499))
  - swarm: fix race condition in TestFailFirst (#1490) ([libp2p/go-libp2p#1490](https://github.com/libp2p/go-libp2p/pull/1490))
  - basichost: fix flaky TestSignedPeerRecordWithNoListenAddrs (#1488) ([libp2p/go-libp2p#1488](https://github.com/libp2p/go-libp2p/pull/1488))
  - swarm: fix flaky and racy TestDialExistingConnection (#1491) ([libp2p/go-libp2p#1491](https://github.com/libp2p/go-libp2p/pull/1491))
  - quic: adjust timeout for reuse garbage collector detection in tests (#1487) ([libp2p/go-libp2p#1487](https://github.com/libp2p/go-libp2p/pull/1487))
  - quic: fix flaky TestResourceManagerAcceptDenied (#1485) ([libp2p/go-libp2p#1485](https://github.com/libp2p/go-libp2p/pull/1485))
  - quic: deflake the holepunching test (#1484) ([libp2p/go-libp2p#1484](https://github.com/libp2p/go-libp2p/pull/1484))
  - holepunch: fix incorrect message type for the SYNC message (#1478) ([libp2p/go-libp2p#1478](https://github.com/libp2p/go-libp2p/pull/1478))
  - use real keys in tests instead of go-libp2p-testing/netutil fake keys (#1475) ([libp2p/go-libp2p#1475](https://github.com/libp2p/go-libp2p/pull/1475))
  - quic: fix flaky TestResourceManagerAcceptDenied ([libp2p/go-libp2p#1461](https://github.com/libp2p/go-libp2p/pull/1461))
  - move go-libp2p-pnet here ([libp2p/go-libp2p#1465](https://github.com/libp2p/go-libp2p/pull/1465))
  - move go-libp2p-tls here ([libp2p/go-libp2p#1466](https://github.com/libp2p/go-libp2p/pull/1466))
  - fix race condition in relayFinder ([libp2p/go-libp2p#1469](https://github.com/libp2p/go-libp2p/pull/1469))
  - fix race condition in holepunch service (#1473) ([libp2p/go-libp2p#1473](https://github.com/libp2p/go-libp2p/pull/1473))
  - Update README to include supported Go Versions (#1470) ([libp2p/go-libp2p#1470](https://github.com/libp2p/go-libp2p/pull/1470))
  - move go-libp2p-noise here ([libp2p/go-libp2p#1462](https://github.com/libp2p/go-libp2p/pull/1462))
  - move go-libp2p-transport-upgrader here ([libp2p/go-libp2p#1463](https://github.com/libp2p/go-libp2p/pull/1463))
  - move go-conn-security-multistream here ([libp2p/go-libp2p#1460](https://github.com/libp2p/go-libp2p/pull/1460))
  - move go-libp2p-mplex here ([libp2p/go-libp2p#1450](https://github.com/libp2p/go-libp2p/pull/1450))
  - use yamux instead of mplex in tests (#1456) ([libp2p/go-libp2p#1456](https://github.com/libp2p/go-libp2p/pull/1456))
  - rename the yamux package (#1452) ([libp2p/go-libp2p#1452](https://github.com/libp2p/go-libp2p/pull/1452))
  - swarm: don't check return value of str.Close in TestResourceManager (#1453) ([libp2p/go-libp2p#1453](https://github.com/libp2p/go-libp2p/pull/1453))
  - move go-libp2p-yamux here ([libp2p/go-libp2p#1439](https://github.com/libp2p/go-libp2p/pull/1439))
  - quic: fix flaky TestConnectionGating test (#1442) ([libp2p/go-libp2p#1442](https://github.com/libp2p/go-libp2p/pull/1442))
  - quic: fix flaky TestReuseGarbageCollect test (#1446) ([libp2p/go-libp2p#1446](https://github.com/libp2p/go-libp2p/pull/1446))
  - quic: fix flaky holepunching test (#1443) ([libp2p/go-libp2p#1443](https://github.com/libp2p/go-libp2p/pull/1443))
  - move go-libp2p-quic-transport here ([libp2p/go-libp2p#1424](https://github.com/libp2p/go-libp2p/pull/1424))
  - remove flaky TestTcpSimultaneousConnect (#1425) ([libp2p/go-libp2p#1425](https://github.com/libp2p/go-libp2p/pull/1425))
  - move go-ws-transport here ([libp2p/go-libp2p#1422](https://github.com/libp2p/go-libp2p/pull/1422))
  - update go-multistream, stop using deprecated NegotiateLazy (#1417) ([libp2p/go-libp2p#1417](https://github.com/libp2p/go-libp2p/pull/1417))
  - fix flaky TestResourceManagerAcceptStream test (#1420) ([libp2p/go-libp2p#1420](https://github.com/libp2p/go-libp2p/pull/1420))
  - move go-tcp-transport here ([libp2p/go-libp2p#1418](https://github.com/libp2p/go-libp2p/pull/1418))
  - move the go-libp2p-swarm here ([libp2p/go-libp2p#1414](https://github.com/libp2p/go-libp2p/pull/1414))
  - reduce flakiness of backoff cache tests (#1415) ([libp2p/go-libp2p#1415](https://github.com/libp2p/go-libp2p/pull/1415))
  - move the go-libp2p-blankhost here ([libp2p/go-libp2p#1411](https://github.com/libp2p/go-libp2p/pull/1411))
- github.com/libp2p/go-libp2p-core (v0.15.1 -> v0.16.1):
  - release v0.16.1 (#255) ([libp2p/go-libp2p-core#255](https://github.com/libp2p/go-libp2p-core/pull/255))
  - force usage of github.com/btcsuite/btcd v0.22.1 or newer (#254) ([libp2p/go-libp2p-core#254](https://github.com/libp2p/go-libp2p-core/pull/254))
  - release v0.16.0 (#251) ([libp2p/go-libp2p-core#251](https://github.com/libp2p/go-libp2p-core/pull/251))
  - remove OpenedStream and ClosedStream from Notifiee interface (#250) ([libp2p/go-libp2p-core#250](https://github.com/libp2p/go-libp2p-core/pull/250))
  - deprecate Negotiator.NegotiateLazy (#249) ([libp2p/go-libp2p-core#249](https://github.com/libp2p/go-libp2p-core/pull/249))
  - update btcec dependency (#247) ([libp2p/go-libp2p-core#247](https://github.com/libp2p/go-libp2p-core/pull/247))
- github.com/libp2p/go-libp2p-discovery (v0.6.0 -> v0.7.0):
  - deprecate this repo (#84) ([libp2p/go-libp2p-discovery#84](https://github.com/libp2p/go-libp2p-discovery/pull/84))
  - remove dependency on the go-libp2p-peerstore/addr package (#82) ([libp2p/go-libp2p-discovery#82](https://github.com/libp2p/go-libp2p-discovery/pull/82))
  - fix flaky TestBackoffDiscoveryMultipleBackoff test on CI (#80) ([libp2p/go-libp2p-discovery#80](https://github.com/libp2p/go-libp2p-discovery/pull/80))
  - chore: update go-log to v2 ([libp2p/go-libp2p-discovery#76](https://github.com/libp2p/go-libp2p-discovery/pull/76))
  - sync: update CI config files (#74) ([libp2p/go-libp2p-discovery#74](https://github.com/libp2p/go-libp2p-discovery/pull/74))
- github.com/libp2p/go-libp2p-swarm (v0.10.2 -> v0.11.0):
  - deprecate this repo (#320) ([libp2p/go-libp2p-swarm#320](https://github.com/libp2p/go-libp2p-swarm/pull/320))
  - sync: update CI config files ([libp2p/go-libp2p-swarm#317](https://github.com/libp2p/go-libp2p-swarm/pull/317))
- github.com/libp2p/go-reuseport (v0.1.0 -> v0.2.0):
  - release v0.2.0 (#90) ([libp2p/go-reuseport#90](https://github.com/libp2p/go-reuseport/pull/90))
  - sync: update CI config files (#86) ([libp2p/go-reuseport#86](https://github.com/libp2p/go-reuseport/pull/86))
- github.com/multiformats/go-multibase (v0.0.3 -> v0.1.0):
  - chore: release v0.1.0
  - feat: add UTF-8 support and base256emoji
  - submodule: spec/
  - sync: update CI config files (#48) ([multiformats/go-multibase#48](https://github.com/multiformats/go-multibase/pull/48))
  - fix staticcheck ([multiformats/go-multibase#41](https://github.com/multiformats/go-multibase/pull/41))
  - Fix vet warnings about conversion of int to string ([multiformats/go-multibase#39](https://github.com/multiformats/go-multibase/pull/39))
- github.com/multiformats/go-multihash (v0.1.0 -> v0.2.0):
  - chore: replace blake2b implementation by golang.org/x/crypto ([multiformats/go-multihash#157](https://github.com/multiformats/go-multihash/pull/157))
  - sync: update CI config files ([multiformats/go-multihash#156](https://github.com/multiformats/go-multihash/pull/156))
- github.com/multiformats/go-multistream (v0.3.0 -> v0.3.3):
  - Release v0.3.3 ([multiformats/go-multistream#90](https://github.com/multiformats/go-multistream/pull/90))
  - Ignore error if can't write back multistream protocol id ([multiformats/go-multistream#89](https://github.com/multiformats/go-multistream/pull/89))
  - release v0.3.2 (#88) ([multiformats/go-multistream#88](https://github.com/multiformats/go-multistream/pull/88))
  - Ignore error if can't write back echoed protocol in negotiate (#87) ([multiformats/go-multistream#87](https://github.com/multiformats/go-multistream/pull/87))
  - release v0.3.1 (#86) ([multiformats/go-multistream#86](https://github.com/multiformats/go-multistream/pull/86))
  - deprecate NegotiateLazy (#85) ([multiformats/go-multistream#85](https://github.com/multiformats/go-multistream/pull/85))
  - return an ErrNotSupported when lazy negotiation fails (#84) ([multiformats/go-multistream#84](https://github.com/multiformats/go-multistream/pull/84))
- github.com/warpfork/go-testmark (v0.9.0 -> v0.10.0):
  - testexec: support a hunk named 'input' for stdin.
  - readme: link to other implementations!
  - readme: discuss autopatching and fixture regeneration
  - readme: discuss extensions, and introduce testexec as an example.

</details>

### Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Marten Seemann | 376 | +11584/-15055 | 894 |
| Jorropo | 18 | +11649/-11249 | 81 |
| noot | 43 | +5974/-3332 | 170 |
| Steven Allen | 173 | +5206/-3124 | 282 |
| Yusef Napora | 49 | +1911/-3606 | 124 |
| Juan Batiz-Benet | 14 | +3933/-53 | 48 |
| Jeromy | 84 | +2140/-1328 | 240 |
| vyzo | 51 | +2057/-1126 | 79 |
| Raúl Kripalani | 39 | +1993/-867 | 103 |
| Jeromy Johnson | 52 | +1700/-1081 | 233 |
| Antonio Navarro Perez | 4 | +1874/-729 | 34 |
| Aarsh Shah | 24 | +1428/-504 | 54 |
| Marcin Rataj | 19 | +1051/-855 | 251 |
| Alex Browne | 25 | +1207/-582 | 49 |
| Jakub Sztandera | 29 | +898/-335 | 63 |
| Friedel Ziegelmayer | 11 | +491/-284 | 18 |
| Will Scott | 6 | +240/-319 | 17 |
| Marco Munizaga | 11 | +377/-141 | 17 |
| Hlib | 8 | +269/-135 | 15 |
| Gus Eggert | 5 | +325/-63 | 19 |
| lnykww | 1 | +275/-50 | 4 |
| Łukasz Magiera | 3 | +196/-58 | 7 |
| Matt Joiner | 14 | +79/-55 | 17 |
| Eric Myhre | 4 | +122/-6 | 5 |
| Andrew Gillis | 1 | +111/-6 | 4 |
| Fazlul Shahriar | 2 | +84/-31 | 5 |
| tg | 1 | +70/-15 | 2 |
| Cory Schwartz | 4 | +50/-28 | 11 |
| Lars Gierth | 3 | +33/-26 | 3 |
| Cole Brown | 2 | +37/-16 | 9 |
| web3-bot | 7 | +38/-11 | 18 |
| Alvin Reyes | 1 | +34/-14 | 1 |
| Hector Sanjuan | 4 | +34/-8 | 5 |
| Guilhem Fanton | 2 | +28/-10 | 6 |
| Brian Meek | 1 | +14/-17 | 4 |
| Hlib Kanunnikov | 1 | +25/-3 | 1 |
| Adin Schmahmann | 5 | +15/-13 | 5 |
| Henrique Dias | 1 | +24/-2 | 4 |
| Dennis Trautwein | 1 | +20/-4 | 2 |
| galargh | 2 | +18/-2 | 2 |
| M. Hawn | 3 | +10/-10 | 7 |
| Can ZHANG | 1 | +12/-3 | 1 |
| Masih H. Derkani | 1 | +4/-10 | 2 |
| gammazero | 1 | +6/-6 | 2 |
| Ikko Ashimine | 1 | +6/-6 | 2 |
| Daniel N | 2 | +6/-5 | 2 |
| watjurk | 1 | +8/-2 | 1 |
| John Steidley | 2 | +4/-4 | 3 |
| Aaron Bieber | 1 | +6/-2 | 1 |
| Kishan Mohanbhai Sagathiya | 1 | +6/-1 | 1 |
| siiky | 3 | +3/-3 | 3 |
| Lucas Molas | 1 | +5/-1 | 1 |
| Kevin Atkinson | 1 | +3/-3 | 1 |
| Aayush Rajasekaran | 1 | +5/-1 | 1 |
| T Mo | 1 | +2/-2 | 1 |
| Piotr Galar | 1 | +2/-2 | 1 |
| Arber Avdullahu | 1 | +2/-2 | 1 |
| Russell Dempsey | 1 | +2/-1 | 1 |
| anders | 1 | +1/-1 | 1 |
| RubenKelevra | 1 | +1/-1 | 1 |
| Jonathan Rudenberg | 1 | +1/-1 | 1 |
| Ettore Di Giacinto | 1 | +2/-0 | 1 |
| Daniel Norman | 1 | +1/-1 | 1 |
| Chawye Hsu | 1 | +1/-1 | 1 |
| Aliabbas Merchant | 1 | +1/-1 | 1 |
| can | 1 | +1/-0 | 1 |
| Ed Mazurek | 1 | +0/-0 | 1 |
