# Kubo changelog v0.29

- [v0.29.0](#v0290)

## v0.29.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [Add search functionality for pin names](#add-search-functionality-for-pin-names)
  - [Customizing `ipfs add` defaults](#customizing-ipfs-add-defaults)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

### 🔦 Highlights

#### Add search functionality for pin names

It is now possible to search for pins by name via `ipfs pin ls --name "SomeName"`.
The search is case-sensitive and will return all pins that contain the specified substring in their name.

> [!TIP]
> The `ipfs pin ls -n` is now a shorthand for `ipfs pin ls --name`, mirroring the behavior of `ipfs pin add`.
> See `ipfs pin ls --help` for more information.

#### Customizing `ipfs add` defaults

This release supports overriding global data ingestion defaults used by commands like `ipfs add` via user-defined [`Import.*` configuration options](../config.md#import).
The hash function, CID version, or UnixFS raw leaves and chunker behaviors can be set once, and used as the new implicit default for `ipfs add`.

> [!TIP]
> As a convenience, two CID [profiles](../config.md#profile) are provided: `legacy-cid-v0` and `test-cid-v1`.
> A test profile that defaults to modern CIDv1 can be applied via `ipfs config profile apply test-cid-v1`.
> We encourage users to try it and report any issues in [kubo#4143](https://github.com/ipfs/kubo/issues/4143).

### 📝 Changelog

<details><summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - fix(cli): unify --name param in ls and add (#10439) ([ipfs/kubo#10439](https://github.com/ipfs/kubo/pull/10439))
  - chore: set version to 0.29.0-rc2
  - fix(libp2p): streams config validation in resource manager (#10435) ([ipfs/kubo#10435](https://github.com/ipfs/kubo/pull/10435))
  - chore: update version
  - chore: libp2p 0.34.1 (#10429) ([ipfs/kubo#10429](https://github.com/ipfs/kubo/pull/10429))
  - refactor: stop using github.com/pkg/errors (#10431) ([ipfs/kubo#10431](https://github.com/ipfs/kubo/pull/10431))
  - chore: fix --help text
  - config: introduce Import section (#10421) ([ipfs/kubo#10421](https://github.com/ipfs/kubo/pull/10421))
  - feat: enables searching pins by name (#10412) ([ipfs/kubo#10412](https://github.com/ipfs/kubo/pull/10412))
  - fix(fuse): ipfs path parsing (#10243) ([ipfs/kubo#10243](https://github.com/ipfs/kubo/pull/10243))
  - core/node: fix divide by zero fatal crash for reprovide rate check (#10411) ([ipfs/kubo#10411](https://github.com/ipfs/kubo/pull/10411))
  - chore: bump to go-ipfs-cmds @ v0.11
  - chore: create next changelog
  - Merge Release: v0.28.0 [skip changelog] ([ipfs/kubo#10402](https://github.com/ipfs/kubo/pull/10402))
  - docs: update release checklist (#10401) ([ipfs/kubo#10401](https://github.com/ipfs/kubo/pull/10401))
  - chore: update version
- github.com/ipfs/boxo (v0.19.0 -> v0.20.0):
  - Release v0.20.0 ([ipfs/boxo#613](https://github.com/ipfs/boxo/pull/613))
- github.com/ipfs/go-blockservice (v0.5.0 -> v0.5.2):
  - docs: remove contribution section
  - chore: bump version
  - chore: deprecate types and readme
  - chore: release v0.5.1
  - fix: remove busyloop in getBlocks by removing batching
- github.com/ipfs/go-ipfs-blockstore (v1.3.0 -> v1.3.1):
  - docs: remove contribution section
  - chore: bump version
  - chore: deprecate types and readme
- github.com/ipfs/go-ipfs-cmds (v0.10.0 -> v0.11.0):
  - chore: release v0.11.0 (#253) ([ipfs/go-ipfs-cmds#253](https://github.com/ipfs/go-ipfs-cmds/pull/253))
  - chore: update deps (#252) ([ipfs/go-ipfs-cmds#252](https://github.com/ipfs/go-ipfs-cmds/pull/252))
  - chore: release 0.10.2 (#251) ([ipfs/go-ipfs-cmds#251](https://github.com/ipfs/go-ipfs-cmds/pull/251))
  - fix(http): return error in case of panic (#250) ([ipfs/go-ipfs-cmds#250](https://github.com/ipfs/go-ipfs-cmds/pull/250))
  - chore: release v0.10.1
- github.com/ipfs/go-ipfs-ds-help (v1.1.0 -> v1.1.1):
  - docs: remove contribution section
  - chore: bump version
  - chore: deprecate types and readme
- github.com/ipfs/go-ipfs-exchange-interface (v0.2.0 -> v0.2.1):
  - chore: bump version
  - Deprecate types and readme (#29) ([ipfs/go-ipfs-exchange-interface#29](https://github.com/ipfs/go-ipfs-exchange-interface/pull/29))
  - docs: Add proper documentation to the interface.
- github.com/ipfs/go-verifcid (v0.0.2 -> v0.0.3):
  - chore: bump version
  - chore: deprecate types and readme
  - Make poseidon hashes good hashes ([ipfs/go-verifcid#19](https://github.com/ipfs/go-verifcid/pull/19))
  - sync: update CI config files (#18) ([ipfs/go-verifcid#18](https://github.com/ipfs/go-verifcid/pull/18))
- github.com/ipld/go-car (v0.5.0 -> v0.6.2):
  - v0.6.2 ([ipld/go-car#464](https://github.com/ipld/go-car/pull/464))
  - fix: opt-in way to allow empty list of roots in CAR headers ([ipld/go-car#461](https://github.com/ipld/go-car/pull/461))
  - feat: add inverse and version to filter cmd ([ipld/go-car#457](https://github.com/ipld/go-car/pull/457))
  - v0.6.1 bump
  - chore: update usage of merkledag by go-car (#437) ([ipld/go-car#437](https://github.com/ipld/go-car/pull/437))
  - feat(cmd/car): add '--no-wrap' option to 'create' command ([ipld/go-car#432](https://github.com/ipld/go-car/pull/432))
  - fix: remove github.com/ipfs/go-ipfs-blockstore dependency
  - feat: expose index for StorageCar
  - perf: reduce NewCarReader allocations
  - fix(deps): update deps for cmd (use master go-car and go-car/v2 for now)
  - fix: new error strings from go-cid
  - fix: tests should match stderr for verbose output
  - fix: reading from stdin should broadcast EOF to block loaders
  - refactor insertion index to be publicly accessible ([ipld/go-car#408](https://github.com/ipld/go-car/pull/408))
  - chore: unmigrate from go-libipfs
  - Create CODEOWNERS
  - blockstore: give a direct access to the index for read operations
  - blockstore: only close the file on error in OpenReadWrite, not OpenReadWriteFile
  - fix: handle (and test) WholeCID vs not; fast Has() path for storage
  - ReadWrite: faster Has() by using the in-memory index instead of reading on disk
  - fix: let `extract` skip missing unixfs shard links
  - fix: error when no files extracted
  - fix: make -f optional, read from stdin if omitted
  - fix: update cmd/car/README with latest description
  - chore: add test cases for extract modes
  - feat: extract accepts '-' as an output path for stdout
  - feat: extract specific path, accept stdin as streaming input
  - fix: if we don't read the full block data, don't error on !EOF
  - blockstore: try to close during Finalize(), even in case of previous error
  - ReadWrite: add an alternative FinalizeReadOnly+Close flow
  - feat: add WithTrustedCar() reader option (#381) ([ipld/go-car#381](https://github.com/ipld/go-car/pull/381))
  - blockstore: fast path for AllKeysChan using the index
  - fix: switch to crypto/rand.Read
  - stop using the deprecated io/ioutil package
  - fix(doc): fix storage package doc formatting
  - fix: return errors for unsupported operations
  - chore: move insertionindex into store pkg
  - chore: add experimental note
  - fix: minor lint & windows fd test problems
  - feat: docs for StorageCar interfaces
  - feat: ReadableWritable; dedupe shared code
  - feat: add Writable functionality to StorageCar
  - feat: StorageCar as a Readable storage, separate from blockstore
  - feat(blockstore): implement a streaming read only storage
  - feat(cmd): add index create subcommand to create an external carv2 index ([ipld/go-car#350](https://github.com/ipld/go-car/pull/350))
  - chore: bump version to 0.6.0
  - fix: use goreleaser instead
  - Allow using WalkOption in WriteCar function ([ipld/go-car#357](https://github.com/ipld/go-car/pull/357))
  - fix: update go-block-format to the version that includes the stubs
  - feat: upgrade from go-block-format to go-libipfs/blocks
  - cleanup readme a bit to make the cli more discoverable (#353) ([ipld/go-car#353](https://github.com/ipld/go-car/pull/353))
  - Update install instructions in README.md
  - Add a debugging form for car files. (#341) ([ipld/go-car#341](https://github.com/ipld/go-car/pull/341))
  -  ([ipld/go-car#340](https://github.com/ipld/go-car/pull/340))
  - add a `SkipNext` method on block reader (#338) ([ipld/go-car#338](https://github.com/ipld/go-car/pull/338))
  - feat: Has() and Get() will respect StoreIdentityCIDs option
- github.com/libp2p/go-libp2p (v0.33.2 -> v0.34.1):
  - release v0.34.1 (#2811) ([libp2p/go-libp2p#2811](https://github.com/libp2p/go-libp2p/pull/2811))
  - config: fix Insecure security constructor (#2810) ([libp2p/go-libp2p#2810](https://github.com/libp2p/go-libp2p/pull/2810))
  - rcmgr: Backwards compatibility if you wrap default impl (#2805) ([libp2p/go-libp2p#2805](https://github.com/libp2p/go-libp2p/pull/2805))
  - v0.34.0 (#2795) ([libp2p/go-libp2p#2795](https://github.com/libp2p/go-libp2p/pull/2795))
  - swarm: fix addr for TestBlackHoledAddrBlocked (#2803) ([libp2p/go-libp2p#2803](https://github.com/libp2p/go-libp2p/pull/2803))
  - Add backwards compatibility with old well-known resource (#2798) ([libp2p/go-libp2p#2798](https://github.com/libp2p/go-libp2p/pull/2798))
  - rcmgr: remove a connection only once from the limiter (#2800) ([libp2p/go-libp2p#2800](https://github.com/libp2p/go-libp2p/pull/2800))
  - Adhere to request.Context when roundtripping on a stream (#2796) ([libp2p/go-libp2p#2796](https://github.com/libp2p/go-libp2p/pull/2796))
  - fix: Set missing deadlines (#2794) ([libp2p/go-libp2p#2794](https://github.com/libp2p/go-libp2p/pull/2794))
  - rcmgr: Add conn_limiter to limit number of conns per ip cidr (#2788) ([libp2p/go-libp2p#2788](https://github.com/libp2p/go-libp2p/pull/2788))
  - identify: refactor observed address manager to do address mapping at thin waist(IP+TCP/UDP) layer (#2793) ([libp2p/go-libp2p#2793](https://github.com/libp2p/go-libp2p/pull/2793))
  - fix: DNS protocol address is not reserved (#2792) ([libp2p/go-libp2p#2792](https://github.com/libp2p/go-libp2p/pull/2792))
  - Update github.com/quic-go/quic-go dependency (#2780) ([libp2p/go-libp2p#2780](https://github.com/libp2p/go-libp2p/pull/2780))
  - webrtc: add webrtc addresses to host normalizer (#2784) ([libp2p/go-libp2p#2784](https://github.com/libp2p/go-libp2p/pull/2784))
  - Add a "Limited" network connectivity state (#2696) ([libp2p/go-libp2p#2696](https://github.com/libp2p/go-libp2p/pull/2696))
  - basichost: append certhash for webrtc addresses provided via address factory (#2774) ([libp2p/go-libp2p#2774](https://github.com/libp2p/go-libp2p/pull/2774))
  - Fix comment (#2775) ([libp2p/go-libp2p#2775](https://github.com/libp2p/go-libp2p/pull/2775))
  - Update: update incomplete readmes (#2767) ([libp2p/go-libp2p#2767](https://github.com/libp2p/go-libp2p/pull/2767))
  - libp2phttp: Return connection: close when doing http over streams (#2756) ([libp2p/go-libp2p#2756](https://github.com/libp2p/go-libp2p/pull/2756))
  - Identify: emit useful events after identification (#2759) ([libp2p/go-libp2p#2759](https://github.com/libp2p/go-libp2p/pull/2759))
  - Update chat with rendezvous example (#2769) ([libp2p/go-libp2p#2769](https://github.com/libp2p/go-libp2p/pull/2769))
  - Rename well-known resource (#2757) ([libp2p/go-libp2p#2757](https://github.com/libp2p/go-libp2p/pull/2757))
  - quic: make server cmd use RFC 9000 instead of draft-29  (#2753) ([libp2p/go-libp2p#2753](https://github.com/libp2p/go-libp2p/pull/2753))
  - autonat: Clean up after close (#2749) ([libp2p/go-libp2p#2749](https://github.com/libp2p/go-libp2p/pull/2749))
  - webrtc: run onDone callback immediately on close (#2729) ([libp2p/go-libp2p#2729](https://github.com/libp2p/go-libp2p/pull/2729))
  - fix: add NullResourceManager to webrtc, fixes panic (#2752) ([libp2p/go-libp2p#2752](https://github.com/libp2p/go-libp2p/pull/2752))
  - feat: add tls KeyLogWriter option (#2750) ([libp2p/go-libp2p#2750](https://github.com/libp2p/go-libp2p/pull/2750))
  - Use any port, not a specific one for examples (#2748) ([libp2p/go-libp2p#2748](https://github.com/libp2p/go-libp2p/pull/2748))
  - quicreuse: remove workaround for quic-go listener close deadlock (#2746) ([libp2p/go-libp2p#2746](https://github.com/libp2p/go-libp2p/pull/2746))
  - use Fx to start and stop the host, swarm, autorelay and quicreuse (#2118) ([libp2p/go-libp2p#2118](https://github.com/libp2p/go-libp2p/pull/2118))
  - webrtc: set sctp receive buffer size to 100kB (#2745) ([libp2p/go-libp2p#2745](https://github.com/libp2p/go-libp2p/pull/2745))
  - basichost: log more info when protocol selection fails (#2734) ([libp2p/go-libp2p#2734](https://github.com/libp2p/go-libp2p/pull/2734))
  - chore: bump quic-go (#2742) ([libp2p/go-libp2p#2742](https://github.com/libp2p/go-libp2p/pull/2742))
  - security: remove unnecessary noise code (#2738) ([libp2p/go-libp2p#2738](https://github.com/libp2p/go-libp2p/pull/2738))
  - webrtc: increase receive buffer size on listener (#2730) ([libp2p/go-libp2p#2730](https://github.com/libp2p/go-libp2p/pull/2730))
  - webrtc: fix bug with logger wrapper (#2727) ([libp2p/go-libp2p#2727](https://github.com/libp2p/go-libp2p/pull/2727))
  - dcutr: fix log format to actually print error (#2725) ([libp2p/go-libp2p#2725](https://github.com/libp2p/go-libp2p/pull/2725))
  - webrtc: use a common logger for all pion logging (#2718) ([libp2p/go-libp2p#2718](https://github.com/libp2p/go-libp2p/pull/2718))
  - chore: remove unreadable code, move a test function to test code, better locking in webrtc control reader
  - ping: use context.Afterfunc to avoid a lingering goroutine (#2723) ([libp2p/go-libp2p#2723](https://github.com/libp2p/go-libp2p/pull/2723))
  - webrtc: close mux when closing listener (#2717) ([libp2p/go-libp2p#2717](https://github.com/libp2p/go-libp2p/pull/2717))
  - webrtc: setup datachannel handlers before connecting to a peer (#2716) ([libp2p/go-libp2p#2716](https://github.com/libp2p/go-libp2p/pull/2716))
- github.com/libp2p/go-libp2p-pubsub (v0.10.0 -> v0.11.0):
  - Fix: Own our CertifiedAddrBook (#555) ([libp2p/go-libp2p-pubsub#555](https://github.com/libp2p/go-libp2p-pubsub/pull/555))
  - chores: bump go-libp2p (#558) ([libp2p/go-libp2p-pubsub#558](https://github.com/libp2p/go-libp2p-pubsub/pull/558))
  - fix: Don't bother parsing an empty slice (#556) ([libp2p/go-libp2p-pubsub#556](https://github.com/libp2p/go-libp2p-pubsub/pull/556))
  - Replace fragmentRPC with appendOrMergeRPC (#557) ([libp2p/go-libp2p-pubsub#557](https://github.com/libp2p/go-libp2p-pubsub/pull/557))
- github.com/multiformats/go-multiaddr (v0.12.3 -> v0.12.4):
  - Release v0.12.4 ([multiformats/go-multiaddr#245](https://github.com/multiformats/go-multiaddr/pull/245))
  - net: restrict unicast ip6 public address space (#235) ([multiformats/go-multiaddr#235](https://github.com/multiformats/go-multiaddr/pull/235))
- github.com/whyrusleeping/cbor-gen (v0.1.0 -> v0.1.1):
  - fix: reduce memory held by deferred objects (#96) ([whyrusleeping/cbor-gen#96](https://github.com/whyrusleeping/cbor-gen/pull/96))

</details>

### 👨‍👩‍👧‍👦 Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Henrique Dias | 33 | +4994/-579 | 115 |
| Rod Vagg | 29 | +3781/-1367 | 90 |
| sukun | 12 | +2026/-1215 | 39 |
| Marco Munizaga | 18 | +1482/-382 | 47 |
| Will | 5 | +769/-213 | 17 |
| Steven Allen | 5 | +540/-115 | 24 |
| Sukun | 4 | +274/-194 | 11 |
| Michael Muré | 7 | +372/-55 | 16 |
| Marten Seemann | 1 | +243/-141 | 10 |
| Marcin Rataj | 7 | +244/-134 | 13 |
| hannahhoward | 1 | +277/-0 | 2 |
| Will Scott | 5 | +54/-38 | 9 |
| Hector Sanjuan | 3 | +68/-20 | 5 |
| Jorropo | 5 | +34/-47 | 15 |
| Andrew Gillis | 2 | +67/-7 | 3 |
| IGP | 1 | +59/-8 | 5 |
| Adin Schmahmann | 2 | +50/-0 | 3 |
| Laurent Senta | 1 | +40/-4 | 2 |
| Brad Fitzpatrick | 1 | +42/-2 | 2 |
| Fabio Bozzo | 1 | +36/-1 | 3 |
| Yolan Romailler | 1 | +15/-19 | 4 |
| Hlib Kanunnikov | 2 | +14/-14 | 6 |
| Andreas Penzkofer | 1 | +22/-2 | 3 |
| Matthias Fasching | 1 | +8/-10 | 1 |
| gopherfarm | 2 | +16/-1 | 2 |
| Dreamacro | 1 | +1/-10 | 1 |
| web3-bot | 2 | +7/-3 | 4 |
| Rafał Leszko | 1 | +4/-4 | 1 |
| Oleg Kovalov | 1 | +4/-4 | 3 |
| dbeal | 1 | +5/-1 | 1 |
| Antonio Navarro Perez | 1 | +4/-1 | 1 |
| dozyio | 1 | +3/-0 | 1 |
| zhiqiangxu | 1 | +1/-1 | 1 |
| the harder the luckier | 1 | +1/-1 | 1 |
| Lukáš Lukáč | 1 | +1/-1 | 1 |
| Steve Loeppky | 1 | +1/-0 | 1 |
