# Kubo changelog v0.24

- [v0.24.0](#v0240)

## v0.24.0

- [Overview](#overview)
- [🔦 Highlights](#-highlights)
  - [Support for content blocking](#support-for-content-blocking)
  - [Gateway: the root of the CARs are no longer meaningful](#gateway-the-root-of-the-cars-are-no-longer-meaningful)
  - [IPNS: improved publishing defaults](#ipns-improved-publishing-defaults)
  - [IPNS: record TTL is used for caching](#ipns-record-ttl-is-used-for-caching)
  - [Experimental Transport: WebRTC Direct](#experimental-transport-webrtc-direct)
- [📝 Changelog](#-changelog)
- [👨‍👩‍👧‍👦 Contributors](#-contributors)

### Overview

### 🔦 Highlights

#### Support for content blocking

This Kubo release ships with built-in content-blocking subsystem [announced earlier this year](https://blog.ipfs.tech/2023-content-blocking-for-the-ipfs-stack/).
Content blocking is an opt-in decision made by the operator of `ipfs daemon`.
The official build does not ship with any denylists.

Learn more at [`/docs/content-blocking.md`](https://github.com/ipfs/kubo/blob/master/docs/content-blocking.md)

#### Gateway: the root of the CARs are no longer meaningful

When requesting a CAR from the gateway, the root of the CAR might no longer be
meaningful. By default, the CAR root will be the last resolvable segment of the
path. However, in situations where the path cannot be resolved, such as when
the path does not exist, a CAR will be sent with a root of `bafkqaaa` (empty CID).
This CAR will contain all blocks necessary to validate that the path does not exist.

#### IPNS: improved publishing defaults

This release changes the default values used when publishing IPNS record
via `ipfs name publish` command:

- Default `--lifetime` increased from `24h` to `48h` to take full advantage of
  the increased expiration window of Amino DHT
  ([go-libp2p-kad-dht#793](https://github.com/libp2p/go-libp2p-kad-dht/pull/793))
- Default `--ttl` increased from `1m` to `1h` to improve website caching and follow
  saner defaults present in similar systems like DNS
  ([specs#371](https://github.com/ipfs/specs/pull/371))

This change only impacts the implicit defaults, when mentioned parameters are omitted
during publishing. Users are free to override the default if different value
makes more sense for their use case.

#### IPNS: record TTL is used for caching

In this release, we've made significant improvements to IPNS caching.

Previously, the TTL value in IPNS records was not utilized, and the
`boxo/namesys` library maintained a static one-minute resolution cache.

With this update, IPNS publishers gain more control over how long a valid IPNS
record remains cached before checking an upstream routing system, such as Amino
DHT, for updates. The TTL value in the IPNS record now serves as a hint for:

- `boxo/namesys`: the internal cache, determining how long the IPNS resolution
  result is cached before asking upstream routing systems for updates.
- `boxo/gateway`: the `Cache-Control` HTTP header in responses to requests made
  for `/ipns/name` content paths.

These changes make it easier for rarely updated IPNS-hosted websites to be
cached more efficiently and load faster in browser contexts.

#### Experimental Transport: WebRTC Direct

This Kubo release includes the initial work towards WebRTC Direct
introduced in [`go-libp2p`](https://github.com/libp2p/go-libp2p/releases/tag/v0.32.0) v0.32:

> [WebRTC Direct](https://github.com/libp2p/specs/blob/master/webrtc/webrtc-direct.md)
> allows browser nodes to connect to go-libp2p nodes directly,
> without any configuration (e.g. TLS certificates) needed on the go-libp2p
> side. This is useful for browser nodes that aren’t able to use
> [WebTransport](https://blog.libp2p.io/2022-12-19-libp2p-webtransport/).

The `/webrtc-direct` transport is disabled by default in Kubo 0.24,
and not ready for production use yet, but we plan to enable it in a future release.

See [`Swarm.Transports.Network.WebRTCDirect`](https://github.com/ipfs/kubo/blob/master/docs/config.md#swarmtransportsnetworkwebrtcdirect)
to learn how to enable it manually, and what current limitations are.

### 📝 Changelog

<details><summary>Full Changelog</summary>

- github.com/ipfs/kubo:
  - chore: update version
  - fix: allow event emitting to happen in parallel with getting the query channel
  - fixes to routing put command (#10205) ([ipfs/kubo#10205](https://github.com/ipfs/kubo/pull/10205))
  - docs: fix accelerated-dht-client
  - docs/config: remove extra commas in PublicGateways example entries
  - chore: update version
  - docs: make it clear Web RTC Direct is experimental
  - feat: add WebRTC Direct support
  - docs: update EARLY_TESTERS.md (#10194) ([ipfs/kubo#10194](https://github.com/ipfs/kubo/pull/10194))
  - Release: v0.24.0-1 ([ipfs/kubo#10190](https://github.com/ipfs/kubo/pull/10190))
- github.com/ipfs/boxo (v0.13.1 -> v0.15.0):
  - Release v0.15.0 ([ipfs/boxo#505](https://github.com/ipfs/boxo/pull/505))
  - Release v0.14.0 ([ipfs/boxo#500](https://github.com/ipfs/boxo/pull/500))
- github.com/ipfs/go-block-format (v0.1.2 -> v0.2.0):
  - v0.2.0 bump
- github.com/ipfs/go-graphsync (v0.15.1 -> v0.16.0):
  - chore: release 0.16.0
  - chore: bump go-libp2p to 0.32.0
- github.com/ipfs/go-ipld-format (v0.5.0 -> v0.6.0):
  - v0.6.0 bump
  - chore: update deps
  - fix: stop using the deprecated io/ioutil package
- github.com/libp2p/go-libp2p (v0.31.0 -> v0.32.1):
  - release v0.32.1 (#2637) ([libp2p/go-libp2p#2637](https://github.com/libp2p/go-libp2p/pull/2637))
  - swarm: fix timer Leak in the dial loop (#2636) ([libp2p/go-libp2p#2636](https://github.com/libp2p/go-libp2p/pull/2636))
  - release v0.32.0 (#2625) ([libp2p/go-libp2p#2625](https://github.com/libp2p/go-libp2p/pull/2625))
  - chore: update js-libp2p examples repo (#2624) ([libp2p/go-libp2p#2624](https://github.com/libp2p/go-libp2p/pull/2624))
  - identify: don't filter dns addresses based on remote addr type (#2553) ([libp2p/go-libp2p#2553](https://github.com/libp2p/go-libp2p/pull/2553))
  - webrtc: fix race in TestRemoveConnByUfrag (#2620) ([libp2p/go-libp2p#2620](https://github.com/libp2p/go-libp2p/pull/2620))
  - swarm: fix recursive resolving of DNS multiaddrs (#2564) ([libp2p/go-libp2p#2564](https://github.com/libp2p/go-libp2p/pull/2564))
  - ci: migrate to renamed interop test action (#2617) ([libp2p/go-libp2p#2617](https://github.com/libp2p/go-libp2p/pull/2617))
  - quic: update quic-go to v0.39.1, set a static resumption token generator key (#2572) ([libp2p/go-libp2p#2572](https://github.com/libp2p/go-libp2p/pull/2572))
  - test/basichost: fix flaky test due to rcmgr (#2613) ([libp2p/go-libp2p#2613](https://github.com/libp2p/go-libp2p/pull/2613))
  - swarm: use typed atomics (#2612) ([libp2p/go-libp2p#2612](https://github.com/libp2p/go-libp2p/pull/2612))
  - swarm: cleanup stream handler goroutine (#2610) ([libp2p/go-libp2p#2610](https://github.com/libp2p/go-libp2p/pull/2610))
  - circuitv2: don't check ASN for private addrs (#2611) ([libp2p/go-libp2p#2611](https://github.com/libp2p/go-libp2p/pull/2611))
  - swarm: use happy eyeballs ranking for TCP dials (#2573) ([libp2p/go-libp2p#2573](https://github.com/libp2p/go-libp2p/pull/2573))
  - webrtc: fix race in TestMuxedConnection (#2607) ([libp2p/go-libp2p#2607](https://github.com/libp2p/go-libp2p/pull/2607))
  - tcp: fix build on riscv64 (#2590) ([libp2p/go-libp2p#2590](https://github.com/libp2p/go-libp2p/pull/2590))
  - Fix missing deprecation tag (#2605) ([libp2p/go-libp2p#2605](https://github.com/libp2p/go-libp2p/pull/2605))
  - swarm: wait for transient connections to upgrade for NewStream (#2542) ([libp2p/go-libp2p#2542](https://github.com/libp2p/go-libp2p/pull/2542))
  - docs: fix typos (#2604) ([libp2p/go-libp2p#2604](https://github.com/libp2p/go-libp2p/pull/2604))
  - webrtc: correctly report incoming packet address on muxed connection (#2586) ([libp2p/go-libp2p#2586](https://github.com/libp2p/go-libp2p/pull/2586))
  - swarm: add loopback to low timeout filter (#2595) ([libp2p/go-libp2p#2595](https://github.com/libp2p/go-libp2p/pull/2595))
  - Fix typos in comments and a test failure message (#2600) ([libp2p/go-libp2p#2600](https://github.com/libp2p/go-libp2p/pull/2600))
  - libp2phttp: don't strip `/` suffix when mounting handler (#2552) ([libp2p/go-libp2p#2552](https://github.com/libp2p/go-libp2p/pull/2552))
  - interop: fix redis env var (#2585) ([libp2p/go-libp2p#2585](https://github.com/libp2p/go-libp2p/pull/2585))
  - quicreuse: remove QUIC metrics tracer (#2582) ([libp2p/go-libp2p#2582](https://github.com/libp2p/go-libp2p/pull/2582))
  - config: warn if connmgr limits conflict with rcmgr (#2527) ([libp2p/go-libp2p#2527](https://github.com/libp2p/go-libp2p/pull/2527))
  - update gomock to v0.3.0 (#2581) ([libp2p/go-libp2p#2581](https://github.com/libp2p/go-libp2p/pull/2581))
  - webrtc: fix deadlock on connection close (#2580) ([libp2p/go-libp2p#2580](https://github.com/libp2p/go-libp2p/pull/2580))
  - webrtc: put buffer back to pool (#2574) ([libp2p/go-libp2p#2574](https://github.com/libp2p/go-libp2p/pull/2574))
  - webrtc: fail Write early if deadline has exceeded before the call (#2578) ([libp2p/go-libp2p#2578](https://github.com/libp2p/go-libp2p/pull/2578))
  - swarm: fix DialPeer behaviour for transient connections (#2547) ([libp2p/go-libp2p#2547](https://github.com/libp2p/go-libp2p/pull/2547))
  - websocket: don't resolve /dnsaddr addresses (#2571) ([libp2p/go-libp2p#2571](https://github.com/libp2p/go-libp2p/pull/2571))
  - core/peer: remove deprecated ID.Pretty method (#2565) ([libp2p/go-libp2p#2565](https://github.com/libp2p/go-libp2p/pull/2565))
  - core/peer: remove deprecated Encode function (#2566) ([libp2p/go-libp2p#2566](https://github.com/libp2p/go-libp2p/pull/2566))
  - mock: use go.uber.org/mock (#2540) ([libp2p/go-libp2p#2540](https://github.com/libp2p/go-libp2p/pull/2540))
  - add WebRTC Direct transport implementation (#2337) ([libp2p/go-libp2p#2337](https://github.com/libp2p/go-libp2p/pull/2337))
  - upgrader: drop support for multistream simultaneous open (#2557) ([libp2p/go-libp2p#2557](https://github.com/libp2p/go-libp2p/pull/2557))
  - examples: stop using deprecated peer.ID.Pretty (#2563) ([libp2p/go-libp2p#2563](https://github.com/libp2p/go-libp2p/pull/2563))
  - swarm: don't dial unspecified addresses (#2560) ([libp2p/go-libp2p#2560](https://github.com/libp2p/go-libp2p/pull/2560))
  - basichost: handle the SetProtocol error in NewStream (#2555) ([libp2p/go-libp2p#2555](https://github.com/libp2p/go-libp2p/pull/2555))
  - libp2phttp: don't initialise ServeMux if not nil (#2548) ([libp2p/go-libp2p#2548](https://github.com/libp2p/go-libp2p/pull/2548))
- github.com/libp2p/go-libp2p-pubsub (v0.9.3 -> v0.10.0):
  - chore: update go-libp2p to v0.32 (#548) ([libp2p/go-libp2p-pubsub#548](https://github.com/libp2p/go-libp2p-pubsub/pull/548))
  - remove usage of deprecated peerid.Pretty method (#542) ([libp2p/go-libp2p-pubsub#542](https://github.com/libp2p/go-libp2p-pubsub/pull/542))
  - Revert "fix: topicscore params can't be set for dynamically subscribed topic (#540)" (#541) ([libp2p/go-libp2p-pubsub#541](https://github.com/libp2p/go-libp2p-pubsub/pull/541))
  - fix: topicscore params can't be set for dynamically subscribed topic (#540) ([libp2p/go-libp2p-pubsub#540](https://github.com/libp2p/go-libp2p-pubsub/pull/540))
- github.com/multiformats/go-multiaddr (v0.11.0 -> v0.12.0):
  - release v0.12.0 (#223) ([multiformats/go-multiaddr#223](https://github.com/multiformats/go-multiaddr/pull/223))
  - net: consider /dns/localhost as private address (#221) ([multiformats/go-multiaddr#221](https://github.com/multiformats/go-multiaddr/pull/221))
  - net: consider dns addresses as public (#220) ([multiformats/go-multiaddr#220](https://github.com/multiformats/go-multiaddr/pull/220))
- github.com/multiformats/go-multistream (v0.4.1 -> v0.5.0):
  - remove support for the simultaneous open extension (#107) ([multiformats/go-multistream#107](https://github.com/multiformats/go-multistream/pull/107))

</details>

### 👨‍👩‍👧‍👦 Contributors

| Contributor | Commits | Lines ± | Files Changed |
|-------------|---------|---------|---------------|
| Henrique Dias | 27 | +4505/-3853 | 244 |
| Marten Seemann | 18 | +4260/-1173 | 101 |
| Sukun | 24 | +1499/-340 | 79 |
| Andrew Gillis | 4 | +169/-1025 | 16 |
| Adin Schmahmann | 4 | +788/-184 | 19 |
| Hector Sanjuan | 6 | +619/-72 | 19 |
| Steven Allen | 11 | +489/-101 | 14 |
| Jorropo | 10 | +221/-192 | 28 |
| Łukasz Magiera | 2 | +306/-9 | 3 |
| Lucas Molas | 1 | +183/-52 | 2 |
| Marcin Rataj | 5 | +160/-25 | 6 |
| piersy | 1 | +57/-0 | 6 |
| Raúl Kripalani | 1 | +25/-25 | 2 |
| Alvin Reyes | 1 | +34/-14 | 1 |
| Dennis Trautwein | 1 | +1/-40 | 2 |
| Icarus9913 | 1 | +14/-14 | 10 |
| Takashi Matsuda | 2 | +18/-1 | 3 |
| gammazero | 4 | +8/-5 | 7 |
| xiaolou86 | 1 | +6/-6 | 5 |
| Daniel Martí | 1 | +9/-2 | 1 |
| Rod Vagg | 3 | +5/-5 | 4 |
| Andrej Manduch | 1 | +5/-5 | 3 |
| vuittont60 | 1 | +4/-4 | 3 |
| vyzo | 1 | +5/-1 | 1 |
| tkzktk | 1 | +3/-3 | 3 |
| tk | 1 | +3/-3 | 2 |
| Prem Chaitanya Prathi | 1 | +1/-5 | 1 |
| Kay | 2 | +2/-3 | 2 |
| Thomas Eizinger | 1 | +2/-2 | 1 |
| Steve Loeppky | 1 | +2/-2 | 1 |
| Jonas Keunecke | 1 | +2/-2 | 1 |
| Alejandro Criado-Pérez | 1 | +1/-1 | 1 |
| web3-bot | 1 | +1/-0 | 1 |
| Eric | 1 | +1/-0 | 1 |
