# EARLY TESTERS PROGRAMME

## What is it?

The early testers programme allows groups using Kubo in production to self-volunteer to help test `kubo` release candidates to ensure that no regressions that might affect production systems make it into the final release. While we invite the _entire_ community to help test releases, members of the early testers program are expected to participate directly and actively in every release.

## What are the expectations?

Members of the early tester program are expected to work closely with us to:

* Provide high quality, actionable feedback.
* Work directly with us to debug regressions in the release.
* Help ensure a rock-solid, timely release.

We will ask early testers to participate at two points in the process:

* When <PERSON><PERSON> enters the second release stage (public beta), early testers will be asked to test <PERSON>bo on non-production infrastructure. This may involve things like:
  - Running integration tests against the release candidate.
  - Running simulations/benchmarks on the release candidate.
  - Manually testing the release candidate to check for regressions.
* When <PERSON><PERSON> enters the third release stage (soft release), early testers will be asked to partially deploy the release candidate to production infrastructure. Release candidates at this stage are expected to be identical to the final release. However, this stage allows the Kubo team to fix any last-minute regressions without cutting an entirely new release.

## Who has signed up?

- [ ] Charity Engine (@rytiss, @tristanolive)
- [ ] Fission (@bmann)
- [ ] Infura (@MichaelMure)
- [ ] OrbitDB (@haydenyoung)
- [ ] Pinata (@obo20)
- [ ] Shipyard (@cewood, @ns4plabs)
- [ ] Siderus (@koalalorenzo)
- [ ] Textile (@sanderpick)
- [ ] @RubenKelevra

## How to sign up?

Simply submit a PR to this document by adding your project name and contact.
