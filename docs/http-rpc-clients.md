# HTTP/RPC Clients

<PERSON><PERSON> provides official HTTP RPC  (`/api/v0`) clients for selected languages:

| Language |     Package Name    | Github Repository                          |
|:--------:|:-------------------:|--------------------------------------------|
| JS       | kubo-rpc-client     | https://github.com/ipfs/js-kubo-rpc-client |
| Go       | `rpc`               | [`../client/rpc`](../client/rpc)           |

There are community-maintained libraries for other languages,
but the Kubo team does provide support for them, YMMV:

- https://docs.ipfs.tech/reference/kubo-rpc-cli/
