<!-- Last updated during [v0.30.0 release](https://github.com/ipfs/kubo/pull/10496) -->

# Items to do upon creating the release issue

- [ ] Fill in the Meta section
- [ ] Assign the issue to the release owner and reviewer.
- [ ] Name the issue "Release vX.Y.Z"
- [ ] Set the proper values for X.Y.Z
- [ ] Pin the issue

<!--
  For each pre-release and final release, copy the [release checklist](docs/RELEASE_CHECKLIST.md)
  in a new comment and replace the title with the correct value. Having a single comment per
  release candidate and final release provides clarity on what steps have already been run per each
  release.
-->

# Meta

* Release owner: @who
* Release reviewer: @who
* Expected RC date: week of YYYY-MM-DD
* 🚢 Expected final release date: YYYY-MM-DD
* Release PR: <add link once release PR is created>
* Accompanying PR for improving the release process: ([example](https://github.com/ipfs/kubo/pull/9391))
* Changelog: https://github.com/ipfs/kubo/blob/master/docs/changelogs/vX.Y.md

# Items In Scope

## Required

<List of items that MUST be included for the release>

## Nice To Have (Optional)

<List of items that MAY be included for the release>
