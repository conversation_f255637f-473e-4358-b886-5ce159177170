version: '3.8'
services:
  ipfs:
    build: .
    restart: unless-stopped
    volumes:
      - ipfs_path:/data/ipfs
      - ipfs_fuse:/ipfs
      - ipns_fuse:/ipns
    environment:
      - IPFS_PATH=/data/ipfs
    ports:
      # Swarm listens on all interfaces, so is remotely reachable.
      - 4001:4001/tcp
      - 4001:4001/udp
      
      # The following ports only listen on the loopback interface, so are not remotely reachable by default.
      # If you want to override these or add more ports, see https://docs.docker.com/compose/extends/ .
      
      # API port, which includes admin operations, so you probably don't want this remotely accessible.
      - 127.0.0.1:5001:5001
      
      # HTTP Gateway
      - 127.0.0.1:8080:8080
volumes:
  ipfs_path:
  ipfs_fuse:
  ipns_fuse:
