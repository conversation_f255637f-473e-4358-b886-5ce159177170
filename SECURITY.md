# Security Policy

The IPFS protocol and its implementations are still in heavy development. This
means that there may be problems in our protocols, or there may be mistakes in
our implementations. We take security
vulnerabilities very seriously. If you discover a security issue, please bring
it to our attention right away!

## Reporting a Vulnerability

If you find a vulnerability that may affect live deployments -- for example, by
exposing a remote execution exploit -- please **send your report privately** to
<EMAIL>. Please **DO NOT file a public issue**.

If the issue is a protocol weakness that cannot be immediately exploited or
something not yet deployed, just discuss it openly.

## Reporting a non security bug

For non-security bugs, please simply file a GitHub [issue](https://github.com/ipfs/go-ipfs/issues/new/choose).
