//go:build !plan9
// +build !plan9

package main

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestIsHidden(t *testing.T) {
	require.True(t, <PERSON><PERSON><PERSON><PERSON>("bar/.git"), "dirs beginning with . should be recognized as hidden")
	require.<PERSON>als<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>("."), ". for current dir should not be considered hidden")
	require.<PERSON>als<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>("bar/baz"), "normal dirs should not be hidden")
}
