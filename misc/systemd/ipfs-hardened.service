# This file will be overwritten on package upgrades, avoid customizations here.
#
# To make persistent changes, create file in 
# "/etc/systemd/system/ipfs.service.d/overwrite.conf" with 
# `systemctl edit ipfs.service`. This file will be parsed after this 
# file has been parsed.
#
# To overwrite a variable, like ExecStart you have to specify it once
# blank and a second time with a new value, like:
# ExecStart=
# ExecStart=/usr/local/bin/ipfs daemon --flag1 --flag2
#
# For more info about custom unit files see systemd.unit(5).

# This service file enables systemd-hardening features compatible with IPFS,
# while breaking compatibility with the fuse-mount function. Use this one only 
# if you don't need the fuse-mount functionality.

[Unit]
Description=InterPlanetary File System (IPFS) daemon
Documentation=https://docs.ipfs.tech/
After=network.target

[Service]
# hardening
ReadWritePaths="/var/lib/ipfs/"
NoNewPrivileges=true
ProtectSystem=strict
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
PrivateDevices=true
DevicePolicy=closed
ProtectControlGroups=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6 AF_NETLINK
ProtectHostname=true
PrivateTmp=true
ProtectClock=true
LockPersonality=true
RestrictNamespaces=true
RestrictRealtime=true
MemoryDenyWriteExecute=true
SystemCallArchitectures=native
SystemCallFilter=@system-service
SystemCallFilter=~@privileged
ProtectHome=true
RemoveIPC=true
RestrictSUIDSGID=true
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

# enable for 1-1024 port listening
#AmbientCapabilities=CAP_NET_BIND_SERVICE 
# enable to specify a custom path see docs/environment-variables.md for further documentations
#Environment=IPFS_PATH=/custom/ipfs/path
# enable to specify a higher limit for open files/connections
#LimitNOFILE=1000000

#don't use swap
MemorySwapMax=0

# Don't timeout on startup. Opening the IPFS repo can take a long time in some cases (e.g., when
# badger is recovering) and migrations can delay startup.
#
# Ideally, we'd be a bit smarter about this but there's no good way to do that without hooking
# systemd dependencies deeper into go-ipfs.
TimeoutStartSec=infinity

Type=notify
User=ipfs
Group=ipfs
StateDirectory=ipfs
Environment=IPFS_PATH="${HOME}"
ExecStart=/usr/local/bin/ipfs daemon --init --migrate
Restart=on-failure
KillSignal=SIGINT

[Install]
WantedBy=default.target
