{"Identity": {"PeerID": "QmNXuBh8HFsWq68Fid8dMbGNQTh7eG6hV9rr1fQyfmfomE", "PrivKey": "CAAS4gQwggJeAgEAAoGBAL+E7A0fcQS9+CHO3YAHj+JzHnWyVA7qqtiAIYbTnp9UvHBb2VFj2Q8eeyKFZD5wHoq3AtOqmIb4TUOMEtWYqXnE8o0T9np8vyCRK5dPn5SVoUw9uax6o2X7OxO1HqTcXHNHGbracawJUdwsk4yuZUpzXLez03yocWwneR0JpVJPAgMBAAECgYAXsa4ygW1OFOKZ7CnjKQxYC738+a8EmWvBlTiQoaXCOI2HqRVdyGiWQkMhpjccsmpU5wdmgHiWWinU7YN3AYgV3cP3qAjyNLBFoxy2dKsS9AOWVwRuuRP12tD05kCCjG4rJAX0JEOClOOtzvQ7/bXarMc3/tMHW7TMLNV8MzcYwQJBAOP9aYSHp8VnsO5j32Ju5SjOQorSdcCweqeUxwlAnXz50KdbNSCMypP3TOt7VeiXTuSITtN44yh+eogF5c4ehycCQQDXDHVmPeBN7uqqqZxZwW5pdeJWvx+REiXXCLE6KEPWlcxbw1D9ublpCCFLYuM68rjq1sjsIVGtiV1tYoMdHJSZAkEA0ddMZ070fB0UHFaQJGktQoGVfXB4MQI94kBtcXanfX/xLBgmre7oBYh4o8TBLXMWigFri/iYG41N+iRzf2NZwQJBAIh8rMpufT2ZZLFaoxRIc4ZVvojmFufhR8j6CFnsElpQivq2tWHEDcx+z3rkUWopgXnzRmSwJQHqTDTPsH26lQkCQQCehmxqaQEwE/QKAI8L8YVolgY2cjUGi6qF/awnI584lDSCuJRU3R/c6nc9R8qljtlJYTtp9iUr8vuN+jt48j+6"}, "Datastore": {"Type": "leveldb", "Path": "/root/.ipfs/datastore"}, "Addresses": {"Swarm": ["/ip4/0.0.0.0/tcp/4011"], "API": "/ip4/127.0.0.1/tcp/5001"}, "Mounts": {"IPFS": "/ipfs", "IPNS": "/ipns", "MFS": "/mfs"}, "Version": {"Current": "0.1.7", "Check": "error", "CheckDate": "0001-01-01T00:00:00Z", "CheckPeriod": "172800000000000", "AutoUpdate": "minor"}, "Bootstrap": []}