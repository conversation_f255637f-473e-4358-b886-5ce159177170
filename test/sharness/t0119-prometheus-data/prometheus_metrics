exchange_bitswap_requests_in_flight
exchange_bitswap_response_bytes_bucket
exchange_bitswap_response_bytes_count
exchange_bitswap_response_bytes_sum
exchange_bitswap_wantlists_items_total
exchange_bitswap_wantlists_seconds_bucket
exchange_bitswap_wantlists_seconds_count
exchange_bitswap_wantlists_seconds_sum
exchange_bitswap_wantlists_total
exchange_httpnet_request_duration_seconds_bucket
exchange_httpnet_request_duration_seconds_count
exchange_httpnet_request_duration_seconds_sum
exchange_httpnet_request_sent_bytes
exchange_httpnet_requests_body_failure
exchange_httpnet_requests_failure
exchange_httpnet_requests_in_flight
exchange_httpnet_requests_total
exchange_httpnet_response_bytes_bucket
exchange_httpnet_response_bytes_count
exchange_httpnet_response_bytes_sum
exchange_httpnet_wantlists_items_total
exchange_httpnet_wantlists_seconds_bucket
exchange_httpnet_wantlists_seconds_count
exchange_httpnet_wantlists_seconds_sum
exchange_httpnet_wantlists_total
go_gc_duration_seconds
go_gc_duration_seconds_count
go_gc_duration_seconds_sum
go_gc_gogc_percent
go_gc_gomemlimit_bytes
go_goroutines
go_info
go_memstats_alloc_bytes
go_memstats_alloc_bytes_total
go_memstats_buck_hash_sys_bytes
go_memstats_frees_total
go_memstats_gc_sys_bytes
go_memstats_heap_alloc_bytes
go_memstats_heap_idle_bytes
go_memstats_heap_inuse_bytes
go_memstats_heap_objects
go_memstats_heap_released_bytes
go_memstats_heap_sys_bytes
go_memstats_last_gc_time_seconds
go_memstats_mallocs_total
go_memstats_mcache_inuse_bytes
go_memstats_mcache_sys_bytes
go_memstats_mspan_inuse_bytes
go_memstats_mspan_sys_bytes
go_memstats_next_gc_bytes
go_memstats_other_sys_bytes
go_memstats_stack_inuse_bytes
go_memstats_stack_sys_bytes
go_memstats_sys_bytes
go_sched_gomaxprocs_threads
go_threads
ipfs_bitswap_active_block_tasks
ipfs_bitswap_active_tasks
ipfs_bitswap_bcast_skips_total
ipfs_bitswap_blocks_received
ipfs_bitswap_haves_received
ipfs_bitswap_pending_block_tasks
ipfs_bitswap_pending_tasks
ipfs_bitswap_recv_all_blocks_bytes_bucket
ipfs_bitswap_recv_all_blocks_bytes_count
ipfs_bitswap_recv_all_blocks_bytes_sum
ipfs_bitswap_recv_dup_blocks_bytes_bucket
ipfs_bitswap_recv_dup_blocks_bytes_count
ipfs_bitswap_recv_dup_blocks_bytes_sum
ipfs_bitswap_send_times_bucket
ipfs_bitswap_send_times_count
ipfs_bitswap_send_times_sum
ipfs_bitswap_sent_all_blocks_bytes_bucket
ipfs_bitswap_sent_all_blocks_bytes_count
ipfs_bitswap_sent_all_blocks_bytes_sum
ipfs_bitswap_want_blocks_total
ipfs_bitswap_wanthaves_broadcast
ipfs_bitswap_wantlist_total
ipfs_bs_cache_boxo_blockstore_cache_hits
ipfs_bs_cache_boxo_blockstore_cache_total
ipfs_fsrepo_datastore_batchcommit_errors_total
ipfs_fsrepo_datastore_batchcommit_latency_seconds_bucket
ipfs_fsrepo_datastore_batchcommit_latency_seconds_count
ipfs_fsrepo_datastore_batchcommit_latency_seconds_sum
ipfs_fsrepo_datastore_batchcommit_total
ipfs_fsrepo_datastore_batchdelete_errors_total
ipfs_fsrepo_datastore_batchdelete_latency_seconds_bucket
ipfs_fsrepo_datastore_batchdelete_latency_seconds_count
ipfs_fsrepo_datastore_batchdelete_latency_seconds_sum
ipfs_fsrepo_datastore_batchdelete_total
ipfs_fsrepo_datastore_batchput_errors_total
ipfs_fsrepo_datastore_batchput_latency_seconds_bucket
ipfs_fsrepo_datastore_batchput_latency_seconds_count
ipfs_fsrepo_datastore_batchput_latency_seconds_sum
ipfs_fsrepo_datastore_batchput_size_bytes_bucket
ipfs_fsrepo_datastore_batchput_size_bytes_count
ipfs_fsrepo_datastore_batchput_size_bytes_sum
ipfs_fsrepo_datastore_batchput_total
ipfs_fsrepo_datastore_check_errors_total
ipfs_fsrepo_datastore_check_latency_seconds_bucket
ipfs_fsrepo_datastore_check_latency_seconds_count
ipfs_fsrepo_datastore_check_latency_seconds_sum
ipfs_fsrepo_datastore_check_total
ipfs_fsrepo_datastore_delete_errors_total
ipfs_fsrepo_datastore_delete_latency_seconds_bucket
ipfs_fsrepo_datastore_delete_latency_seconds_count
ipfs_fsrepo_datastore_delete_latency_seconds_sum
ipfs_fsrepo_datastore_delete_total
ipfs_fsrepo_datastore_du_errors_total
ipfs_fsrepo_datastore_du_latency_seconds_bucket
ipfs_fsrepo_datastore_du_latency_seconds_count
ipfs_fsrepo_datastore_du_latency_seconds_sum
ipfs_fsrepo_datastore_du_total
ipfs_fsrepo_datastore_gc_errors_total
ipfs_fsrepo_datastore_gc_latency_seconds_bucket
ipfs_fsrepo_datastore_gc_latency_seconds_count
ipfs_fsrepo_datastore_gc_latency_seconds_sum
ipfs_fsrepo_datastore_gc_total
ipfs_fsrepo_datastore_get_errors_total
ipfs_fsrepo_datastore_get_latency_seconds_bucket
ipfs_fsrepo_datastore_get_latency_seconds_count
ipfs_fsrepo_datastore_get_latency_seconds_sum
ipfs_fsrepo_datastore_get_size_bytes_bucket
ipfs_fsrepo_datastore_get_size_bytes_count
ipfs_fsrepo_datastore_get_size_bytes_sum
ipfs_fsrepo_datastore_get_total
ipfs_fsrepo_datastore_getsize_errors_total
ipfs_fsrepo_datastore_getsize_latency_seconds_bucket
ipfs_fsrepo_datastore_getsize_latency_seconds_count
ipfs_fsrepo_datastore_getsize_latency_seconds_sum
ipfs_fsrepo_datastore_getsize_total
ipfs_fsrepo_datastore_has_errors_total
ipfs_fsrepo_datastore_has_latency_seconds_bucket
ipfs_fsrepo_datastore_has_latency_seconds_count
ipfs_fsrepo_datastore_has_latency_seconds_sum
ipfs_fsrepo_datastore_has_total
ipfs_fsrepo_datastore_put_errors_total
ipfs_fsrepo_datastore_put_latency_seconds_bucket
ipfs_fsrepo_datastore_put_latency_seconds_count
ipfs_fsrepo_datastore_put_latency_seconds_sum
ipfs_fsrepo_datastore_put_size_bytes_bucket
ipfs_fsrepo_datastore_put_size_bytes_count
ipfs_fsrepo_datastore_put_size_bytes_sum
ipfs_fsrepo_datastore_put_total
ipfs_fsrepo_datastore_query_errors_total
ipfs_fsrepo_datastore_query_latency_seconds_bucket
ipfs_fsrepo_datastore_query_latency_seconds_count
ipfs_fsrepo_datastore_query_latency_seconds_sum
ipfs_fsrepo_datastore_query_total
ipfs_fsrepo_datastore_scrub_errors_total
ipfs_fsrepo_datastore_scrub_latency_seconds_bucket
ipfs_fsrepo_datastore_scrub_latency_seconds_count
ipfs_fsrepo_datastore_scrub_latency_seconds_sum
ipfs_fsrepo_datastore_scrub_total
ipfs_fsrepo_datastore_sync_errors_total
ipfs_fsrepo_datastore_sync_latency_seconds_bucket
ipfs_fsrepo_datastore_sync_latency_seconds_count
ipfs_fsrepo_datastore_sync_latency_seconds_sum
ipfs_fsrepo_datastore_sync_total
ipfs_http_gw_concurrent_requests
ipfs_http_request_duration_seconds
ipfs_http_request_duration_seconds_count
ipfs_http_request_duration_seconds_sum
ipfs_http_request_size_bytes
ipfs_http_request_size_bytes_count
ipfs_http_request_size_bytes_sum
ipfs_http_requests_total
ipfs_http_response_size_bytes
ipfs_http_response_size_bytes_count
ipfs_http_response_size_bytes_sum
ipfs_info
libp2p_autonat_next_probe_timestamp
libp2p_autonat_reachability_status
libp2p_autonat_reachability_status_confidence
libp2p_autorelay_candidate_loop_state
libp2p_autorelay_candidates_circuit_v2_support_total
libp2p_autorelay_desired_reservations
libp2p_autorelay_relay_addresses_count
libp2p_autorelay_relay_addresses_updated_total
libp2p_autorelay_reservation_requests_outcome_total
libp2p_autorelay_reservations_closed_total
libp2p_autorelay_reservations_opened_total
libp2p_autorelay_status
libp2p_eventbus_events_emitted_total
libp2p_eventbus_subscriber_event_queued
libp2p_eventbus_subscriber_queue_full
libp2p_eventbus_subscriber_queue_length
libp2p_eventbus_subscribers_total
libp2p_holepunch_address_outcomes_total
libp2p_holepunch_outcomes_total
libp2p_identify_addrs_count
libp2p_identify_addrs_received_bucket
libp2p_identify_addrs_received_count
libp2p_identify_addrs_received_sum
libp2p_identify_identify_pushes_triggered_total
libp2p_identify_protocols_count
libp2p_identify_protocols_received_bucket
libp2p_identify_protocols_received_count
libp2p_identify_protocols_received_sum
libp2p_rcmgr_conn_memory_bucket
libp2p_rcmgr_conn_memory_count
libp2p_rcmgr_conn_memory_sum
libp2p_rcmgr_connections
libp2p_rcmgr_fds
libp2p_rcmgr_peer_connections_bucket
libp2p_rcmgr_peer_connections_count
libp2p_rcmgr_peer_connections_sum
libp2p_rcmgr_peer_memory_bucket
libp2p_rcmgr_peer_memory_count
libp2p_rcmgr_peer_memory_sum
libp2p_rcmgr_peer_streams_bucket
libp2p_rcmgr_peer_streams_count
libp2p_rcmgr_peer_streams_sum
libp2p_rcmgr_previous_conn_memory_bucket
libp2p_rcmgr_previous_conn_memory_count
libp2p_rcmgr_previous_conn_memory_sum
libp2p_rcmgr_previous_peer_connections_bucket
libp2p_rcmgr_previous_peer_connections_count
libp2p_rcmgr_previous_peer_connections_sum
libp2p_rcmgr_previous_peer_memory_bucket
libp2p_rcmgr_previous_peer_memory_count
libp2p_rcmgr_previous_peer_memory_sum
libp2p_rcmgr_previous_peer_streams_bucket
libp2p_rcmgr_previous_peer_streams_count
libp2p_rcmgr_previous_peer_streams_sum
libp2p_relaysvc_connection_duration_seconds_bucket
libp2p_relaysvc_connection_duration_seconds_count
libp2p_relaysvc_connection_duration_seconds_sum
libp2p_relaysvc_data_transferred_bytes_total
libp2p_relaysvc_status
libp2p_swarm_dial_ranking_delay_seconds_bucket
libp2p_swarm_dial_ranking_delay_seconds_count
libp2p_swarm_dial_ranking_delay_seconds_sum
process_cpu_seconds_total
process_max_fds
process_network_receive_bytes_total
process_network_transmit_bytes_total
process_open_fds
process_resident_memory_bytes
process_start_time_seconds
process_virtual_memory_bytes
process_virtual_memory_max_bytes
provider_reprovider_provide_count
provider_reprovider_reprovide_count
