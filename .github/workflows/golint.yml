name: Go <PERSON>t

on:
  workflow_dispatch:
  pull_request:
    paths-ignore:
      - '**/*.md'
  push:
    branches:
      - 'master'

concurrency:
  group: ${{ github.workflow }}-${{ github.event_name }}-${{ github.event_name == 'push' && github.sha || github.ref }}
  cancel-in-progress: true

jobs:
  go-lint:
    if: github.repository == 'ipfs/kubo' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      TEST_DOCKER: 0
      TEST_FUSE: 0
      TEST_VERBOSE: 1
      TRAVIS: 1
      GIT_PAGER: cat
      IPFS_CHECK_RCMGR_DEFAULTS: 1
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: 1.25.x
      - uses: actions/checkout@v5
      - run: make -O test_go_lint
