name: Interop

on:
  workflow_dispatch:
  pull_request:
    paths-ignore:
      - '**/*.md'
  push:
    branches:
      - 'master'

env:
  GO_VERSION: 1.25.x

concurrency:
  group: ${{ github.workflow }}-${{ github.event_name }}-${{ github.event_name == 'push' && github.sha || github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  interop-prep:
    if: github.repository == 'ipfs/kubo' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    timeout-minutes: 5
    env:
      TEST_DOCKER: 0
      TEST_FUSE: 0
      TEST_VERBOSE: 1
      TRAVIS: 1
      GIT_PAGER: cat
      IPFS_CHECK_RCMGR_DEFAULTS: 1
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
      - uses: actions/checkout@v5
      - run: make build
      - uses: actions/upload-artifact@v4
        with:
          name: kubo
          path: cmd/ipfs/ipfs
  helia-interop:
    needs: [interop-prep]
    runs-on: ${{ fromJSON(github.repository == 'ipfs/kubo' && '["self-hosted", "linux", "x64", "2xlarge"]' || '"ubuntu-latest"') }}
    timeout-minutes: 20
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - uses: actions/download-artifact@v5
        with:
          name: kubo
          path: cmd/ipfs
      - run: chmod +x cmd/ipfs/ipfs
      - run: echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
        id: npm-cache-dir
      - uses: actions/cache@v4
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-${{ github.job }}-helia-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.os }}-${{ github.job }}-helia-
      - run: sudo apt update
      - run: sudo apt install -y libxkbcommon0 libxdamage1 libgbm1 libpango-1.0-0 libcairo2 # dependencies for playwright
      - run: npx --package @helia/interop helia-interop
        env:
          KUBO_BINARY: ${{ github.workspace }}/cmd/ipfs/ipfs
  ipfs-webui:
    needs: [interop-prep]
    runs-on: ${{ fromJSON(github.repository == 'ipfs/kubo' && '["self-hosted", "linux", "x64", "2xlarge"]' || '"ubuntu-latest"') }}
    timeout-minutes: 20
    env:
      NO_SANDBOX: true
      LIBP2P_TCP_REUSEPORT: false
      LIBP2P_ALLOW_WEAK_RSA_KEYS: 1
      E2E_IPFSD_TYPE: go
      TRAVIS: 1
      GIT_PAGER: cat
      IPFS_CHECK_RCMGR_DEFAULTS: 1
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x
      - uses: actions/download-artifact@v5
        with:
          name: kubo
          path: cmd/ipfs
      - run: chmod +x cmd/ipfs/ipfs
      - uses: actions/checkout@v5
        with:
          repository: ipfs/ipfs-webui
          path: ipfs-webui
      - run: |
          echo "dir=$(npm config get cache)" >> $GITHUB_OUTPUT
        id: npm-cache-dir
      - uses: actions/cache@v4
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-${{ github.job }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-${{ github.job }}-
      - env:
          NPM_CACHE_DIR: ${{ steps.npm-cache-dir.outputs.dir }}
        run: |
          npm ci --prefer-offline --no-audit --progress=false --cache "$NPM_CACHE_DIR"
          npx playwright install --with-deps
        working-directory: ipfs-webui
      - id: ref
        run: echo "ref=$(git rev-parse --short HEAD)" | tee -a $GITHUB_OUTPUT
        working-directory: ipfs-webui
      - id: state
        env:
          GITHUB_TOKEN: ${{ github.token }}
          ENDPOINT: repos/ipfs/ipfs-webui/commits/${{ steps.ref.outputs.ref }}/status
          SELECTOR: .state
          KEY: state
        run: gh api "$ENDPOINT" --jq "$SELECTOR" | xargs -I{} echo "$KEY={}" | tee -a $GITHUB_OUTPUT
      - name: Build ipfs-webui@main (state=${{ steps.state.outputs.state }})
        run: npm run test:build
        working-directory: ipfs-webui
      - name: Test ipfs-webui@main (state=${{ steps.state.outputs.state }}) E2E against the locally built Kubo binary
        run: npm run test:e2e
        env:
          IPFS_GO_EXEC: ${{ github.workspace }}/cmd/ipfs/ipfs
        working-directory: ipfs-webui
