# https://docs.github.com/en/free-pro-team@latest/github/finding-security-vulnerabilities-and-errors-in-your-code/configuring-code-scanning#changing-the-languages-that-are-analyzed
name: CodeQL

on:
  workflow_dispatch:
  push:
    branches: [ master ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ master ]
    paths-ignore:
      - '**/*.md'
  schedule:
    - cron: '30 12 * * 2'

permissions:
  contents: read  #  to fetch code (actions/checkout)
  security-events: write  #   (github/codeql-action/autobuild)

concurrency:
  group: ${{ github.workflow }}-${{ github.event_name }}-${{ github.event_name == 'push' && github.sha || github.ref }}
  cancel-in-progress: true

jobs:
  codeql:
    if: github.repository == 'ipfs/kubo' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
    - name: Checkout repository
      uses: actions/checkout@v5

    - name: Setup Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.25.x

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: go

    - name: Autobuild
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
