ratings:
  paths: 
  - "**/*.go"

exclude_paths:
- test/
- Godeps/
- thirdparty/
- "**/*.pb.go"

engines:
  fixme:
    enabled: true
    config:
      strings:
      - FIXME
      - HACK
      - XXX
      - BUG
  golint:
    enabled: true
  govet:
    enabled: true
  gofmt:
    enabled: true

version: "2"
checks:
  argument-count:
    enabled: false
  complex-logic:
    enabled: false
  file-lines:
    enabled: false
  method-complexity:
    enabled: false
  method-count:
    enabled: false
  method-lines:
    enabled: false
  nested-control-flow:
    enabled: false
  return-statements:
    enabled: false
  similar-code:
    enabled: false
